name: Update Test Server

on:
  workflow_run:
    workflows: ["Docker Dev Images Build"]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: |
      github.repository == 'danny-avila/LibreChat' &&
      (github.event_name == 'workflow_dispatch' || github.event.workflow_run.conclusion == 'success')
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install SSH Key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.DO_SSH_PRIVATE_KEY }}
        known_hosts: ${{ secrets.DO_KNOWN_HOSTS }}

    - name: Run update script on DigitalOcean Droplet
      env:
        DO_HOST: ${{ secrets.DO_HOST }}
        DO_USER: ${{ secrets.DO_USER }}
      run: |
        ssh -o StrictHostKeyChecking=no ${DO_USER}@${DO_HOST} << EOF
        sudo -i -u danny bash << EEOF
        cd ~/LibreChat && \
        git fetch origin main && \
        npm run update:deployed && \
        git checkout do-deploy && \
        git rebase main && \
        npm run start:deployed && \
        echo "Update completed. Application should be running now."
        EEOF
        EOF
