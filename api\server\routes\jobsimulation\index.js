const express = require('express');
const router = express.Router();
const adminRoutes = require('./admin');
const progressRoutes = require('./progress');
const userRoutes = require('./user');
const publicRoutes = require('./public');
const userInteractionRoutes = require('./user-interaction');

// Admin routes
router.use('/admin', adminRoutes);

// User interaction routes
router.use('/user-interaction', userInteractionRoutes);

// User progress
// TODO: refactor /user-interaction and /user-progress to /user
router.use('/user-progress', progressRoutes);
router.use('/user', userRoutes);

// Public routes
// TODO: add middleware check x-api-key
router.use('/public', publicRoutes);

module.exports = router;
