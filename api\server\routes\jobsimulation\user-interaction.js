const express = require('express');
const router = express.Router();
const JobSimulationInteractionController = require('~/server/controllers/JobSimulationInteractionController');
const { requireJwtAuth } = require('~/server/middleware');

router.use(requireJwtAuth);

router.get('/', JobSimulationInteractionController.getUserInteraction);
router.post('/', JobSimulationInteractionController.saveUserInteraction);
router.delete('/', JobSimulationInteractionController.deleteUserInteraction);

module.exports = router;
