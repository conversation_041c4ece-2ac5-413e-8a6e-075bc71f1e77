import { Copy } from 'lucide-react';
import { useLocalize } from '~/hooks';
import { useToastContext } from '~/Providers';
import { EditIcon, Clipboard, CheckMark, ContinueIcon, RegenerateIcon } from '~/components/svg';

export type ButtonProps = {
  title: string;
  text: string;
  delay?: number;
};

const CopyMessageButton = ({ title, text }: ButtonProps) => {
  const { showToast } = useToastContext();
  const localize = useLocalize();

  const handleClick = () => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showToast({
          status: 'success',
          message: localize('com_ui_copied'),
        });
      })
      .catch((err) => {
        console.error('Failed to copy text: ', text, err);
      });
  };

  return (
    <div className="group relative my-4 rounded-xl text-sm text-text-primary">
      {/* <button
        type="button"
        title={text}
        onClick={handleClick}
        className="relative overflow-hidden rounded-xl border border-border-medium transition-all duration-300 hover:border-border-xheavy hover:shadow-lg"
      >
        <div className="w-fit bg-surface-tertiary p-2">
          <div className="flex flex-row items-center gap-2">
            <div className="overflow-hidden text-left">
              <div className="truncate font-medium">{title}</div>
              <div className="truncate text-text-secondary">Click to copy</div>
            </div>
          </div>
        </div>
      </button> */}
      <div className="flex flex-row items-center gap-2" onClick={handleClick}>
        <div className="truncate font-medium">{text}</div>
        <div className="cursor-pointer">
          <Clipboard />
        </div>
      </div>
    </div>
  );
};

export default CopyMessageButton;
