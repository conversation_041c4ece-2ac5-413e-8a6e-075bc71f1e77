import React from 'react';
import { MoreH<PERSON>zontal, MessageCircle, ThumbsUp, MessageSquare, Share, X, Info } from 'lucide-react';
import { Button } from '~/components/ui';

const AdPreviewSection = () => {
	return (
		<div className="space-y-6">
			{/* Preview */}
			<div className='bg-white drop-shadow-lg rounded-lg'>
				<div className="flex items-center justify-between p-4">
					<p className='text-lg font-medium'>Ad Preview</p>
					<Button variant="ghost" className='text-sm font-normal text-blue-400 hover:text-blue-500'>View All Previews</Button>
				</div>
				<hr />
				<div className='p-4'>
					{/* Facebook Post Preview */}
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center space-x-2">
							<div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
								H
							</div>
							<div>
								<div className="font-medium text-base">Hello everyone with i porn 12</div>
								<div className="text-xs text-gray-500">Sponsored • 👥</div>
							</div>
						</div>
						<div className="flex items-center space-x-4">
							<MoreHorizontal className="w-6 h-6" />
							<X />
						</div>
					</div>

					<div className="mb-3">
						<p className="text-sm">Hello Everyone</p>
					</div>

					{/* WORKS by design image */}
					<div className="mb-3">
						<img
							src="/lovable-uploads/4250a041-a051-4269-9da6-0545ff40a6ee.png"
							alt="WORKS by design"
							className="w-full rounded-lg"
						/>
					</div>

					<div className="flex items-center justify-between text-sm bg-gray-50 px-3 py-4 mb-3">
						<div className="flex items-center space-x-1">
							<span>Messenger</span>
						</div>
						<div className="flex items-center space-x-1 rounded-lg bg-gray-200 px-3 py-2 font-medium">
							<MessageCircle className="w-6 h-6" />
							<span>Send message</span>
						</div>
					</div>
					<hr />
					<div className="w-full text-sm text-gray-600 mt-2">
						<div className="flex items-center justify-center space-x-10 font-medium">
							<div className="flex items-center space-x-1">
								<ThumbsUp className="w-4 h-4" />
								<span>Like</span>
							</div>
							<div className="flex items-center space-x-1">
								<MessageSquare className="w-4 h-4" />
								<span>Comment</span>
							</div>
							<div className="flex items-center space-x-1">
								<Share className="w-4 h-4" />
								<span>Share</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Daily Reach Estimate */}
			<div className='bg-white drop-shadow-lg p-4 rounded-lg'>
				<p className="text-lg font-medium mb-3">
					Estimated daily results
				</p>
				<div className='space-y-3'>
					<div className='bg-gray-50 p-3 rounded-lg'>
						<div className='flex items-center justify-between mb-2'>
							<p className='text-xs'><span className='text-blue-500 cursor-pointer hover:underline'>Accounts Center accounts</span> reached</p>
							<Info className="w-4 h-4 text-gray-400" />
						</div>
						<p className='text-base font-medium'>97.1K - 280.5K</p>
					</div>
					<div className='bg-gray-50 p-3 rounded-lg'>
						<div className='flex items-center justify-between mb-2'>
							<p className='text-xs'>Replies</p>
							<Info className="w-4 h-4 text-gray-400" />
						</div>
						<p className='text-base font-medium'>57 - 166</p>
					</div>
				</div>
			</div>

			{/* Payment Summary */}
			<div className='bg-white drop-shadow-lg p-4 rounded-lg'>
				<div className='mb-4'>
					<p className="text-lg font-medium">
						Payment summary
					</p>
					<p className="text-sm">Your ad will run for 7 days.</p>
				</div>

				<div className="space-y-3 bg-gray-50 rounded-lg p-3">
					<div className="flex justify-between font-normal">
						<span className="text-sm">Total Budget</span>
						<span className="text-sm">104,379 ₫ (VND)</span>
					</div>
					<p className='text-xs font-normal text-[#929294]'>₫3000000 a day x 7 days.</p>
					<div className="flex justify-between font-normal">
						<span className="text-sm">Estimated VAT</span>
						<span className="text-sm">1,050,000 ₫ (VND)</span>
					</div>
					<div className="border-t pt-3">
						<div className="flex justify-between font-normal">
							<span className="text-sm">Total amount</span>
							<span className="text-sm">22,050,000 ₫ (VND)</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AdPreviewSection;