import { atom } from 'recoil';

const jobSimulationIframe = atom<{
  url: string;
  type: 'virtual-world' | 'normal';
  visible: boolean;
} | null>({
  key: 'jobSimulationIframe',
  default: null,
});

const jobSimulationVirtualWorldMessage = atom<string | null>({
  key: 'jobSimulationVirtualWorldMessage',
  default: null,
});

export default {
  jobSimulationIframe,
  jobSimulationVirtualWorldMessage,
};
