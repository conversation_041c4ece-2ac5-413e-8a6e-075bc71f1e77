const express = require('express');
const router = express.Router();
const JobSimulationPublicController = require('~/server/controllers/JobSimulationPublicController');
const { checkClient } = require('~/server/middleware');

router.use(checkClient);

router.get('/list', JobSimulationPublicController.getJobSimulations);
router.post('/external/create-user', JobSimulationPublicController.externalRegistration);

module.exports = router;
