import * as Ariakit from '@ariakit/react';
import { easings } from '@react-spring/web';
import { motion } from 'framer-motion';
import { LogOut, Moon, Sun } from 'lucide-react';
import { memo, useContext, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import type * as t from '~/common';
import {
  Button,
  DropdownMenuSeparator,
  DropdownPopup,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
  SplitText,
} from '~/components';
import { CheckMark, Spinner } from '~/components/svg';
import { useUserJobSimulationsInfo } from '~/data-provider/JobSimulation/queries';
import { ThemeContext, useAuthContext, useMediaQuery } from '~/hooks';
import useAvatar from '~/hooks/Messages/useAvatar';
import { cn } from '~/utils';

// Types for job simulations and agents
interface JobSimulation {
  id: string;
  jobSimulationId: string;
  name: string;
  description: string;
  logo: string;
  companyName: string;
  participants: number;
  agentId?: string;
  progress?: {
    status: string;
    intakeId: string;
    email: string;
  };
}

interface Agent {
  id: string;
  name: string;
  avatar: {
    filepath: string;
  };
}

// Sample data for job simulations
const sampleJobSimulations: JobSimulation[] = [
  {
    id: '6818de396eae2c750ea99df3',
    jobSimulationId: 'esg-analyst',
    name: 'ESG Analyst',
    description:
      'Work as an ESG Analyst at Greentek Industries, analyzing environmental, social, and governance factors to help companies improve their sustainability practices.',
    logo: '/assets/greentek-logo.png',
    companyName: 'Greentek Industries',
    participants: 123,
    agentId: 'agent_bv2AXZ8dCbC00kOWSj5R2',
  },
  {
    id: '68193c186eae2c750ea99df4',
    jobSimulationId: 'digital-marketing',
    name: 'Digital Marketing Analyst',
    description:
      'Experience the role of a Digital Marketing Specialist at BrightWave Media, creating and implementing marketing strategies for various clients.',
    // logo: 'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1747191396074__logo.png',
    logo: 'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/682d9af55a3f459586c280b3/1747923073715__logo.png',
    companyName: 'BrightWave Media',
    participants: 1500,
    agentId: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
  },
  // {
  //   id: '68193c186eae2c750ea99df5',
  //   jobSimulationId: 'data-analyst',
  //   name: 'Data Analyst',
  //   description:
  //     'Work as a Data Analyst at DataTech Solutions, analyzing complex datasets to derive insights and support business decisions.',
  //   logo: '/assets/datatech-logo.png',
  //   companyName: 'DataTech Solutions',
  //   participants: 875,
  //   agentId: 'agent_bv2AXZ8dCbC00kOWSj5R2',
  // },
  // {
  //   id: '68193c186eae2c750ea99df6',
  //   jobSimulationId: 'product-manager',
  //   name: 'Product Manager',
  //   description:
  //     'Experience the role of a Product Manager at InnovateTech, leading product development from conception to launch.',
  //   logo: '/assets/innovatetech-logo.png',
  //   companyName: 'InnovateTech',
  //   participants: 642,
  //   agentId: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
  // },
  // {
  //   id: '68193c186eae2c750ea99df7',
  //   jobSimulationId: 'ux-designer',
  //   name: 'UX Designer',
  //   description:
  //     'Work as a UX Designer at DesignHub, creating user-centered designs for web and mobile applications.',
  //   logo: '/assets/designhub-logo.png',
  //   companyName: 'DesignHub',
  //   participants: 389,
  //   agentId: 'agent_bv2AXZ8dCbC00kOWSj5R2',
  // },
  // {
  //   id: '68193c186eae2c750ea99df8',
  //   jobSimulationId: 'software-engineer',
  //   name: 'Software Engineer',
  //   description:
  //     'Experience the role of a Software Engineer at CodeCraft, developing and maintaining software applications.',
  //   logo: '/assets/codecraft-logo.png',
  //   companyName: 'CodeCraft',
  //   participants: 1250,
  //   agentId: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
  // },
];

// Sample data for agents
const sampleAgents: Agent[] = [
  {
    id: 'agent_bv2AXZ8dCbC00kOWSj5R2',
    name: 'Victor Lee',
    avatar: {
      filepath: 'https://beta.bitmeet.io/files/1747796592616-*********-111.jpg',
    },
  },
  {
    id: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
    name: 'Mia',
    avatar: {
      filepath: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-2.jpg',
    },
  },
];

// Mock API calls with timeout to simulate loading
const getJobs = async (): Promise<JobSimulation[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(sampleJobSimulations);
    }, 1500);
  });
};

const getAgents = async (): Promise<Agent[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(sampleAgents);
    }, 1000);
  });
};

// Job card component
const JobCard = ({
  job,
  agent,
  onViewDetails,
  onViewCertification,
}: {
  job: JobSimulation;
  agent?: Agent;
  onViewDetails: (job: JobSimulation) => void;
  onViewCertification: (job: JobSimulation) => void;
}) => {
  return (
    <motion.div
      className="animate-fadeIn flex w-full flex-col rounded-2xl border border-border-light bg-white p-4 shadow-sm transition-all duration-200 hover:shadow-md dark:bg-gray-700"
      whileHover={{ scale: 1.03 }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
    >
      <div className="relative mb-4 flex h-52 items-center justify-center rounded-2xl bg-black">
        <img
          src="/assets/job-logo.png"
          alt={`${job.name} logo`}
          className="max-h-[80%] max-w-[80%] object-contain"
        />
        <h3 className="absolute bottom-4 left-4 text-2xl font-semibold text-white">{job.name}</h3>
      </div>

      <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
        at <span className="font-semibold">{job.companyName}</span>
      </p>

      <p className="mb-3 line-clamp-3 h-[4.5em] text-sm text-gray-600 dark:text-gray-300">
        {job.description}
      </p>

      {/* <div className="mb-3 flex items-center text-sm text-gray-500 dark:text-gray-400">
        <span>{job.participants.toLocaleString()} participants</span>
      </div> */}

      {/* {agent && (
        <div className="mb-4 flex items-center gap-2">
          <img
            src={agent.avatar.filepath}
            alt={agent.name}
            className="h-8 w-8 rounded-full object-cover"
          />
          <span className="text-sm font-semibold">{agent.name}</span>
        </div>
      )} */}

      <div className="mt-auto flex flex-row gap-2">
        {job.progress?.status === 'completed' ? (
          <>
            <span className="flex flex-1 items-center justify-start gap-2 text-[#00C224]">
              <CheckMark />
              <span>Completed</span>
            </span>

            <Button
              onClick={() => onViewCertification(job)}
              variant="outline"
              className="h-auto w-auto rounded-2xl border-job-primary px-3 py-3"
            >
              View certification
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => onViewDetails(job)}
              variant="outline"
              className="h-auto w-full rounded-2xl px-3 py-3"
            >
              View details
            </Button>

            <Link
              to={`/job-simulation/${job.jobSimulationId}`}
              className={cn(
                'flex w-full items-center justify-center rounded-2xl bg-job-primary px-3 py-3 text-center text-sm font-medium text-black',
                'hover:bg-job-primary-700 focus:outline-none focus:ring-2 focus:ring-job-primary focus:ring-offset-2',
              )}
            >
              {job.progress?.status === 'active' ? 'Resume' : 'Give it a try'}
            </Link>
          </>
        )}
      </div>
    </motion.div>
  );
};

function ListJobSimulationPage() {
  const { isAuthenticated } = useAuthContext();
  // const [jobs, setJobs] = useState<JobSimulation[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  // const [isLoading, setIsLoading] = useState(true);
  const [selectedJob, setSelectedJob] = useState<JobSimulation | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isOpenCertificationDialog, setIsOpenCertificationDialog] = useState(false);
  const [certificationData, setCertificationData] = useState<{
    intakeId: string;
    email: string;
  } | null>({
    intakeId: '',
    email: '',
  });

  const { data = [], isLoading: isLoadingData } = useUserJobSimulationsInfo(
    { page: 1, limit: 6 },
    { enabled: isAuthenticated },
  );
  const jobs: JobSimulation[] = data;
  console.log('useUserJobSimulationsInfo ::: data: ', jobs);

  // Fetch jobs and agents
  useEffect(() => {
    const fetchData = async () => {
      // setIsLoading(true);
      try {
        // const [jobsData, agentsData] = await Promise.all([getJobs(), getAgents()]);
        const agentsData = await getAgents();

        // setJobs(jobsData);
        setAgents(agentsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        // setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewDetails = (job: JobSimulation) => {
    setSelectedJob(job);
    setIsDialogOpen(true);
  };

  const handleViewCertification = (job: JobSimulation) => {
    if (!job.progress?.intakeId || !job.progress?.email) return;
    setCertificationData({
      intakeId: job.progress.intakeId,
      email: job.progress.email,
    });
    setIsOpenCertificationDialog(true);
  };

  const handleCloseCertification = () => {
    setIsOpenCertificationDialog(false);
    setCertificationData(null);
  };

  const findAgentForJob = (job: JobSimulation) => {
    return agents.find((agent) => agent.id === job.agentId);
  };

  return (
    <div className="relative flex h-auto w-full flex-col px-4 py-8 lg:min-h-screen">
      {/* Image at the top, centered */}
      <div className="mb-9 flex w-full justify-center">
        <img
          src="/assets/list-job-logo.png"
          alt="list job"
          className="h-auto max-h-52 max-w-full"
        />
      </div>

      <h1 className="mb-10 text-center text-3xl font-bold dark:text-white">
        {isLoadingData ? (
          // 'Getting jobs ready...'
          <SplitText
            key={`split-text-getting-ready`}
            text="Getting jobs ready..."
            className="text-3xl font-medium text-text-primary"
            delay={50}
            textAlign="center"
            animationFrom={{ opacity: 0, transform: 'translate3d(0,50px,0)' }}
            animationTo={{ opacity: 1, transform: 'translate3d(0,0,0)' }}
            easing={easings.easeOutCubic}
            threshold={0}
            rootMargin="0px"
          />
        ) : (
          <SplitText
            key={`split-text-select-job`}
            text="Select a job you want to experience."
            className="text-3xl font-medium text-text-primary"
            delay={50}
            textAlign="center"
            animationFrom={{ opacity: 0, transform: 'translate3d(0,50px,0)' }}
            animationTo={{ opacity: 1, transform: 'translate3d(0,0,0)' }}
            easing={easings.easeOutCubic}
            threshold={0}
            rootMargin="0px"
          />
        )}
      </h1>

      {isLoadingData ? (
        <div className="flex h-40 w-full items-center justify-center">
          <Spinner className="h-10 w-10 text-blue-600" />
        </div>
      ) : (
        <div className="flex w-full justify-center">
          <div className="mx-auto grid w-full max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {jobs.length === 1 ? (
              <div className="col-span-full flex w-full justify-center md:mx-auto md:max-w-md">
                <JobCard
                  key={jobs[0].id}
                  job={jobs[0]}
                  agent={findAgentForJob(jobs[0])}
                  onViewDetails={handleViewDetails}
                  onViewCertification={handleViewCertification}
                />
              </div>
            ) : jobs.length === 2 ? (
              <div className="col-span-full grid w-full grid-cols-1 gap-6 sm:grid-cols-2 md:mx-auto md:max-w-3xl">
                {jobs.slice(0, 2).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </div>
            ) : jobs.length <= 6 ? (
              <div className="col-span-full grid w-full grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {jobs.slice(0, 6).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </div>
            ) : (
              <>
                {jobs.slice(0, 6).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </>
            )}
          </div>
        </div>
      )}

      {/* Job details dialog */}
      <OGDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <OGDialogContent className="max-w-md">
          <OGDialogHeader>
            <OGDialogTitle>{selectedJob?.name}</OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 p-4">
            {selectedJob && (
              <>
                <div className="flex items-center gap-4">
                  <img
                    src={selectedJob.logo}
                    alt={`${selectedJob.name} logo`}
                    className="h-16 w-16 object-contain"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/assets/logo.png';
                    }}
                  />
                  <div>
                    <h3 className="text-lg font-semibold">{selectedJob.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {selectedJob.companyName}
                    </p>
                  </div>
                </div>

                <div className="rounded-md bg-gray-50 p-4 dark:bg-gray-700">
                  <p className="text-sm">{selectedJob.description}</p>
                </div>

                {selectedJob.participants !== undefined && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Participants:</span>
                    <span className="text-sm">{selectedJob.participants.toLocaleString()}</span>
                  </div>
                )}

                {findAgentForJob(selectedJob) && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Assistant:</span>
                    <div className="flex items-center gap-2">
                      <img
                        src={findAgentForJob(selectedJob)?.avatar.filepath}
                        alt={findAgentForJob(selectedJob)?.name}
                        className="h-8 w-8 rounded-full object-cover"
                      />
                      <span className="text-sm">{findAgentForJob(selectedJob)?.name}</span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          <OGDialogFooter>
            <Link
              to={`/job-simulation/${selectedJob?.jobSimulationId}`}
              className={cn(
                'flex w-full items-center justify-center rounded-2xl bg-job-primary px-3 py-3 text-center text-sm font-medium text-black',
                'hover:bg-job-primary-700 focus:outline-none focus:ring-2 focus:ring-job-primary focus:ring-offset-2',
              )}
            >
              Give it a try
            </Link>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
      <CertificationDialog
        isOpen={isOpenCertificationDialog}
        setIsOpen={handleCloseCertification}
        intakeId={certificationData?.intakeId || ''}
        email={certificationData?.email || ''}
      />

      <div className="fixed bottom-0 left-0 md:m-4">
        <UserProfileDropdown />
      </div>
    </div>
  );
}

function CertificationDialog(params: {
  intakeId: string;
  email: string;
  isOpen: boolean;
  setIsOpen: (isOpen?: boolean) => void;
}) {
  const { isOpen, setIsOpen, intakeId, email } = params;
  const url =
    intakeId && email
      ? `https://uat.internship.guru/en/public/reference-letter?programId=${intakeId}&secret=${email}`
      : '';
  return (
    <OGDialog open={isOpen} onOpenChange={setIsOpen}>
      <OGDialogContent className="h-screen max-w-6xl">
        {/* <OGDialogHeader>
          <OGDialogTitle>Certification</OGDialogTitle>
        </OGDialogHeader> */}

        <div className="flex flex-col">
          {url && (
            <iframe
              src={url}
              className="h-full w-full border-0"
              allow="camera; microphone; display-capture; clipboard-read; clipboard-write; fullscreen"
            />
          )}
        </div>
      </OGDialogContent>
    </OGDialog>
  );
}

function UserProfileDropdown() {
  const [isPopoverActive, setIsPopoverActive] = useState(false);
  const { theme, setTheme } = useContext(ThemeContext);
  const { user, logout } = useAuthContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const menuId = 'user-profile-menu';

  const avatarSrc = useAvatar(user);
  const avatarSeed = user?.avatar || user?.name || user?.username || '';

  const themeHandler = () => {
    const nextTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(nextTheme);
    setIsPopoverActive(false);
  };

  const logoutHandler = () => {
    logout();
  };

  const dropdownItems: t.MenuItemProps[] = [
    {
      // label: user?.email,
      render: (_props) => (
        <div className="p-2">
          {user?.email}
          <DropdownMenuSeparator />
        </div>
      ),
      hideOnClick: false,
    },
    {
      label: theme === 'dark' ? 'Light Theme' : 'Dark Theme',
      onClick: themeHandler,
      icon: {
        dark: <Sun className="icon-md mr-2 text-text-secondary" />,
        light: <Moon className="icon-md mr-2 text-text-secondary" />,
      }[theme],
      hideOnClick: true,
    },
    {
      label: 'Logout',
      onClick: logoutHandler,
      icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
  ];

  return (
    <div className="flex items-center">
      <DropdownPopup
        menuId={menuId}
        focusLoop={true}
        isOpen={isPopoverActive}
        setIsOpen={setIsPopoverActive}
        trigger={
          <Ariakit.MenuButton
            id="user-profile-button"
            aria-label="User profile options"
            className="flex items-center gap-2 rounded-lg bg-white p-2 text-text-primary shadow-sm transition-all ease-in-out hover:bg-surface-tertiary dark:bg-gray-700 dark:text-white"
          >
            <div className="relative h-8 w-8 overflow-hidden rounded-full">
              {!avatarSeed ? (
                <div
                  className="relative flex h-full w-full items-center justify-center rounded-full bg-primary/10 p-1 text-text-primary"
                  aria-hidden="true"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
              ) : (
                <img
                  className="h-full w-full rounded-full object-cover"
                  src={(user?.avatar ?? '') || avatarSrc}
                  alt={`${user?.name || user?.username || user?.email || ''}'s avatar`}
                />
              )}
            </div>
            <span className="hidden text-sm md:block">
              {user?.name ?? user?.username ?? 'User'}
            </span>
          </Ariakit.MenuButton>
        }
        items={dropdownItems}
        className={isSmallScreen ? '' : 'absolute bottom-0 left-0'}
      />
    </div>
  );
}

export default memo(ListJobSimulationPage);
