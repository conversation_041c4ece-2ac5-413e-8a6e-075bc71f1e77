const axios = require('axios');

const API_URL = 'https://portal.agentos.cloud/api/v1';

const AgentosExternalService = {
	async fetchAgents({ projectId, page = 1, size = 10 }) {
		// console.log("🚀 ~ fetchAgents ~  projectId, page = 1, size = 10:", projectId, page = 1, size = 10)
		const url = `${API_URL}/agents?projectId=${projectId}&page=${page}&size=${size}`;
		const { data } = await axios.get(url, {
			headers: {
				'x-skip-auth': 'true',
				'Accept': 'application/json'
			},
		});
		// console.log("🚀 ~ fetchAgents ~ data:", data)
		return data;
	},
};

module.exports = AgentosExternalService;