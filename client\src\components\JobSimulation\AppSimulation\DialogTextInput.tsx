import {
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialog<PERSON>ooter,
  OGDialogHeader,
  OGDialogTitle,
  Textarea,
} from '~/components';

interface IDialogTextInputProps {
  textInputData: {
    type: string;
    value: string;
    dataContextId: string;
    saveToSelections: boolean;
    dataContextLabel: string;
  };
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
  onInputChange: (value: string) => void;
  onSubmit: (value?: any) => void;
}

const DialogTextInput = (props: IDialogTextInputProps) => {
  const { isOpen, textInputData, onInputChange, onSubmit, onOpenChange } = props;

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <>
      <OGDialog open={isOpen} onOpenChange={onOpenChange}>
        <OGDialogContent
          title="Enter Text"
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              Enter your {textInputData?.dataContextLabel || 'text content'}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="textInput">{textInputData?.dataContextLabel || 'Text Content'}</Label>
              {textInputData?.type === 'textarea' ? (
                <Textarea
                  id="textInput"
                  value={textInputData.value}
                  onChange={(e) => {
                    onInputChange(e.target.value);
                  }}
                  onKeyDown={handleKeyDown}
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter value here..."
                  rows={5}
                />
              ) : (
                <Input
                  id="textInput"
                  value={textInputData.value}
                  onChange={(e) => {
                    onInputChange(e.target.value);
                  }}
                  onKeyDown={handleKeyDown}
                  className="transition-all duration-200"
                  placeholder="Enter value here..."
                />
              )}
            </div>
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => onOpenChange(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={onSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default DialogTextInput;
