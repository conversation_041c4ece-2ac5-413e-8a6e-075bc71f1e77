name: Detect Unused i18next Strings

on:
  pull_request:
    paths:
      - "client/src/**"
      - "api/**"

jobs:
  detect-unused-i18n-keys:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write  # Required for posting PR comments
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Find unused i18next keys
        id: find-unused
        run: |
          echo "🔍 Scanning for unused i18next keys..."

          # Define paths
          I18N_FILE="client/src/locales/en/translation.json"
          SOURCE_DIRS=("client/src" "api")

          # Check if translation file exists
          if [[ ! -f "$I18N_FILE" ]]; then
            echo "::error title=Missing i18n File::Translation file not found: $I18N_FILE"
            exit 1
          fi

          # Extract all keys from the JSON file
          KEYS=$(jq -r 'keys[]' "$I18N_FILE")

          # Track unused keys
          UNUSED_KEYS=()

          # Check if each key is used in the source code
          for KEY in $KEYS; do
            FOUND=false
            for DIR in "${SOURCE_DIRS[@]}"; do
              if grep -r --include=\*.{js,jsx,ts,tsx} -q "$KEY" "$DIR"; then
                FOUND=true
                break
              fi
            done
          
            if [[ "$FOUND" == false ]]; then
              UNUSED_KEYS+=("$KEY")
            fi
          done

          # Output results
          if [[ ${#UNUSED_KEYS[@]} -gt 0 ]]; then
            echo "🛑 Found ${#UNUSED_KEYS[@]} unused i18n keys:"
            echo "unused_keys=$(echo "${UNUSED_KEYS[@]}" | jq -R -s -c 'split(" ")')" >> $GITHUB_ENV
            for KEY in "${UNUSED_KEYS[@]}"; do
              echo "::warning title=Unused i18n Key::'$KEY' is defined but not used in the codebase."
            done
          else
            echo "✅ No unused i18n keys detected!"
            echo "unused_keys=[]" >> $GITHUB_ENV
          fi

      - name: Post verified comment on PR
        if: env.unused_keys != '[]'
        run: |
          PR_NUMBER=$(jq --raw-output .pull_request.number "$GITHUB_EVENT_PATH")

          # Format the unused keys list as checkboxes for easy manual checking.
          FILTERED_KEYS=$(echo "$unused_keys" | jq -r '.[]' | grep -v '^\s*$' | sed 's/^/- [ ] `/;s/$/`/' )

          COMMENT_BODY=$(cat <<EOF
          ### 🚨 Unused i18next Keys Detected

          The following translation keys are defined in \`translation.json\` but are **not used** in the codebase:

          $FILTERED_KEYS

          ⚠️ **Please remove these unused keys to keep the translation files clean.**
          EOF
          )

          gh api "repos/${{ github.repository }}/issues/${PR_NUMBER}/comments" \
            -f body="$COMMENT_BODY" \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Fail workflow if unused keys found
        if: env.unused_keys != '[]'
        run: exit 1