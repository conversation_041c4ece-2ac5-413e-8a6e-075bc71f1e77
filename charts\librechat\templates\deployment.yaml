apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "librechat.fullname" . }}
  labels:
    {{- include "librechat.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "librechat.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "librechat.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "librechat.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            {{ if .Values.config.envSecrets.secretRef }}
            - secretRef:
                name: {{ .Values.config.envSecrets.secretRef }}
            {{- end }}
            - configMapRef:
                name: {{ include "librechat.fullname" . }}-env
          env:
            {{- range $secretKeyRef := .Values.config.envSecrets.secretKeyRef }}
            - name: {{ $secretKeyRef.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secretKeyRef.secretName }}
                  key: {{ $secretKeyRef.secretKey }}
            {{- end }}
          ports:
            - name: http
              containerPort: 3080
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 5
            httpGet:
              path: /
              port: http
          readinessProbe:
            initialDelaySeconds: 5
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
