import ReactMarkdown from 'react-markdown';

export default function EmailReview({ values }) {

	const preview = `
## ${values.title}

**${values.sender.name} - ${values.sender.role}**  
${values.sender.email}

---

${values.greeting}

${values.content}

---

${values.signature}
`;

	return (
		<div className="rounded border border-black px-6 py-4 bg-white w-full mx-auto">
			<h1 className="text-lg font-handwriting">{values.title || 'Welcome to Job Simulation!'}</h1>
			<hr className="my-3 border-t-2 border-black" />
			<div className="flex items-center gap-3 mb-4">
				{values.sender.avatar ? (
					<img
						src={values.sender.avatar}
						alt="avatar"
						className="w-8 h-8 rounded-full border border-black object-cover"
					/>
				) : (
					<div className="w-8 h-8 rounded-full border border-black bg-white" />
				)}
				<div className="text-sm">
					<div className="font-bold">
						{values.sender.name || '{sender_name}'} - {values.sender.role || '{sender_role}'}
					</div>
					<div>{values.sender.email || '{sender_email}'}</div>
				</div>
			</div>
			<div className="whitespace-pre-wrap text-sm space-y-2">
				<p>{values.greeting || 'Hi {username}'}</p>
				<ReactMarkdown>
					{values.content || '{content}'}
				</ReactMarkdown>
				{values.type === 'Meeting' && (
					<p className="font-semibold">[Action here: Meeting link or info]</p>
				)}
			</div>
			<div className="mt-6 whitespace-pre-wrap text-sm space-y-1">
				<p>{values.signature || 'Warm regards,\n{name} - {role}'}</p>
			</div>
		</div>
	);
}