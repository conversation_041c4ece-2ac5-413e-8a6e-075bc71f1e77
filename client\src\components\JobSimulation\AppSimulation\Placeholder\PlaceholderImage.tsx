import { AppSimulationScreenPlaceholder } from '~/common';
import { getPlaceholderStyle } from '~/components/JobSimulation/AppSimulation/AppSimulationHelpers';

interface PlaceholderImageProps {
  choosedImage?: string;
  placeholder: AppSimulationScreenPlaceholder;
}

const PlaceholderImage = ({ choosedImage, placeholder }: PlaceholderImageProps) => {
  const imagePath = choosedImage || placeholder.initialValue;
  if (!imagePath) return null;

  return (
    <>
      <div style={getPlaceholderStyle(placeholder)}>
        <img
          src={imagePath}
          alt="image"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            ...placeholder.imgStyle,
          }}
        />
      </div>
    </>
  );
};

export default PlaceholderImage;
