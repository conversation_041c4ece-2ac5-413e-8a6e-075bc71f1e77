import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, Search, Filter, MoreHorizontal, Calendar, Trash2 } from 'lucide-react';
import { Button } from '~/components/ui';
import DateRangeDropdown from './components/DateRangeDropdown';

const AdsList = ({ onGoToDraft, onGoToPostDetail }) => {
	const [showDateDropdown, setShowDateDropdown] = useState(false);
	const [selectedDateRange, setSelectedDateRange] = useState('Last 30 days');

	const ads = [
		{
			id: 1,
			name: 'Facebook Ad Increase Messages',
			status: 'Draft',
			reach: '28 copies',
			impressions: '73 message views',
			results: '--',
			amountSpent: '--',
			lastUpdate: '16:08 May 29, 2025'
		}
	];

	return (
		<div className='h-full'>
			{/* Main Content */}
			<div className='bg-white rounded-lg p-4 drop-shadow-lg'>
				{/* Header */}
				<div className="mb-6">
					<div className="flex items-center justify-between mb-4">
						<div className="flex items-center space-x-4">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2" />
								<input
									type="text"
									placeholder="Search"
									className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
								/>
							</div>
							<Button variant="outline" className="flex items-center space-x-2">
								<Filter className="w-4 h-4" />
								<span>Filter</span>
							</Button>
							<Button variant="outline">Clear</Button>
						</div>

						<div className="relative">
							<Button
								variant="outline"
								onClick={() => setShowDateDropdown(!showDateDropdown)}
								className="flex items-center space-x-2 font-normal"
							>
								<Calendar className="w-4 h-4" />
								<span>Year: May 30, 2024 - May 29, 2025</span>
								<ChevronDown className="w-4 h-4" />
							</Button>

							{showDateDropdown && (
								<DateRangeDropdown
									selectedRange={selectedDateRange}
									onSelect={(range) => {
										setSelectedDateRange(range);
										setShowDateDropdown(false);
									}}
									onClose={() => setShowDateDropdown(false)}
								/>
							)}
						</div>
					</div>
				</div>

				{/* Ads Table */}
				<div className="">
					{/* Table Header */}
					<div className="grid grid-cols-6 gap-4 p-4 border-t border-b text-base">
						<div className="font-medium">Ads</div>
						<div className="font-medium">Views</div>
						<div className="font-medium">Reach</div>
						<div className="font-medium">Results</div>
						<div className="font-medium">Amount Spent</div>
						<div></div>
					</div>

					{/* Table Body */}
					{ads.map((ad) => (
						<div key={ad.id} className="grid grid-cols-6 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50">
							<div className='flex items-center cursor-pointer' onClick={onGoToPostDetail}>
								<div className='max-w-[209px]'>
									<p className='text-xs text-[#929294] mb-1'>Boosted Facebook post</p>
									<div className="flex items-center space-x-3">
										<div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
											W
										</div>
										<div className='flex-1 text-[#929294] text-xs'>
											<p>Expires in 28 days</p>
											<p className='text-base text-black'>Draft: Get more messages</p>
											<p>Content</p>
											<p>Last updated May 29, 2025, 4:08 PM</p>
										</div>
									</div>
								</div>
								<p className='flex-1 text-sm font-medium bg-[#e1edf7] rounded-xl text-center px-2 py-0.5'>Draft</p>
							</div>
							<div className="flex items-center text-gray-700">

							</div>

							<div className="flex items-center text-gray-700">
							</div>

							<div className="flex items-center text-gray-700">
							</div>

							<div className="flex items-center text-gray-700">
							</div>

							<div className="flex items-center justify-end space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={onGoToDraft}
									className="font-normal rounded px-3"
								>
									Edit draft
								</Button>
								<Button variant="ghost" size="sm" className='border rounded'>
									<Trash2 className="w-4 h-4" />
								</Button>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default AdsList;