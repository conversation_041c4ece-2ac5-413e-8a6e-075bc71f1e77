const axios = require('axios');

const BitMeetService = {
  async getOrCreateBitMeetUser({ username, email, avatar, name }) {
    const url = `${process.env.BITMEET_API_BASE_URL}/v1/public/users`;
    const { data } = await axios.post(
      url,
      {
        id: username,
        email,
        avatar,
        displayName: name,
      },
      {
        headers: {
          'X-API-KEY': process.env.BITMEET_API_KEY,
          Accept: 'application/json',
        },
      },
    );
    return data;
  },
};

module.exports = BitMeetService;
