import React, { useState } from 'react';
import { Calendar, Clock } from 'lucide-react';
import { Button } from '~/components/ui';

interface ScheduleOptionsProps {
	selectedMode: 'now' | 'schedule' | 'draft';
	onModeChange: (mode: 'now' | 'schedule' | 'draft') => void;
}

const ScheduleOptions: React.FC<ScheduleOptionsProps> = ({
	selectedMode,
	onModeChange,
}) => {
	const [scheduleDate, setScheduleDate] = useState('2025-05-29');
	const [scheduleTime, setScheduleTime] = useState('16:19');

	const buttons = [
		{ id: 'now', label: 'Publish now' },
		{ id: 'schedule', label: 'Schedule' },
		{ id: 'draft', label: 'Save as Draft' },
	];

	return (
		<div className='bg-white drop-shadow-xl rounded p-4'>
			<div className='flex items-center justify-between'>
				<p className="text-base font-medium">
					Schedule Options
				</p>
				<div className="flex space-x-2">
					{buttons.map((button) => (
						<button
							key={button.id}
							onClick={() => onModeChange(button.id as 'now' | 'schedule' | 'draft')}
							className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${selectedMode === button.id
								? 'bg-gray-200 border-gray-300 text-gray-800'
								: 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
								}`}
						>
							{button.label}
						</button>
					))}
				</div>
			</div>

			{selectedMode === 'schedule' && (
				<div className="mt-4">
					<p className="text-sm text-gray-700 mb-4">
						Schedule your post for the times when your audience is most active, or manually select a date and time in the future to publish your post.
					</p>

					<div className="flex items-center space-x-2 text-sm font-medium text-gray-800 mb-2">
						<div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
							<span className="text-white text-xs font-bold">f</span>
						</div>
						<span>Facebook</span>
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div>
							<div className="relative">
								<input
									type="date"
									value={scheduleDate}
									onChange={(e) => setScheduleDate(e.target.value)}
									className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
								/>
							</div>
						</div>

						<div className='mb-4'>
							<div className="relative">
								<input
									type="time"
									value={scheduleTime}
									onChange={(e) => setScheduleTime(e.target.value)}
									className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
								/>
							</div>
						</div>
					</div>

					<Button variant="outline" className="flex items-center space-x-2 font-medium">
						<Clock className="w-4 h-4 text-gray-500" />
						<span>Activity Time</span>
					</Button>
				</div>
			)}
		</div>
	);
};

export default ScheduleOptions;