const { SystemRoles } = require('librechat-data-provider');
const { HeaderAPIKeyStrategy } = require('passport-headerapikey');
const { Client } = require('~/models');
const { logger } = require('~/config');

const clientAPIKeyCheck = async () =>
  new HeaderAPIKeyStrategy(
    {
      header: 'Authorization', prefix: 'Bearer '
    },
    false,
    async (apiKey, done) => {
      try {
        const check = await Client.checkAPIKey(apiKey);
        if (check) {
          done(null, true);
        } else {
          logger.warn('HeaderAPIKeyStrategy => no client found: ' + api<PERSON>ey);
          done(null, false);
        }
      } catch (err) {
        done(err);
      }
    },
  );

module.exports = {
  clientAPIKeyCheck,
}
