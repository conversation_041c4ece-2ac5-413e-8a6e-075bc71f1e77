import React, { memo, useMemo, useState, useEffect } from 'react';
import {
  SandpackPreview,
  SandpackProvider,
  SandpackProviderProps,
} from '@codesandbox/sandpack-react/unstyled';
import type { SandpackPreviewRef } from '@codesandbox/sandpack-react/unstyled';
import type { ArtifactFiles } from '~/common';
import { sharedFiles, sharedOptions } from '~/utils/artifacts';
import { useGetStartupConfig } from '~/data-provider';
import { useEditorContext } from '~/Providers';

export const ArtifactPreview = memo(function ({
  files,
  fileKey,
  previewRef,
  sharedProps,
  template,
}: {
  files: ArtifactFiles;
  fileKey: string;
  template: SandpackProviderProps['template'];
  sharedProps: Partial<SandpackProviderProps>;
  previewRef: React.MutableRefObject<SandpackPreviewRef>;
}) {
  const { currentCode } = useEditorContext();
  const { data: config } = useGetStartupConfig();
  const [isLoading, setIsLoading] = useState(true);

  const artifactFiles = useMemo(() => {
    if (Object.keys(files).length === 0) {
      return files;
    }
    const code = currentCode ?? '';
    if (!code) {
      return files;
    }
    return {
      ...files,
      [fileKey]: {
        code,
      },
    };
  }, [currentCode, files, fileKey]);

  const options: typeof sharedOptions = useMemo(() => {
    if (!config) {
      return sharedOptions;
    }
    return {
      ...sharedOptions,
      bundlerURL: config.bundlerURL,
    };
  }, [config]);

  const checkIframeReady = () => {
    const iframe = previewRef.current?.iframe;
    if (!iframe) return false;

    try {
      const innerIframes = iframe.contentDocument?.querySelectorAll('iframe');
      if (!innerIframes?.length) return true;

      return Array.from(innerIframes).every((inner) => {
        try {
          return inner.contentWindow?.document?.readyState === 'complete';
        } catch {
          return false;
        }
      });
    } catch {
      return false;
    }
  };


  const handleIframeLoad = () => {
    const container = previewRef.current?.element; // Native wrapper của SandpackPreview
    if (!container) {
      setIsLoading(false);
      return;
    }

    const iframe = container.querySelector('iframe'); // Query chính xác iframe sandbox trong DOM
    if (!iframe?.contentDocument?.body) {
      setIsLoading(false);
      return;
    }

    const checkReady = () => {
      try {
        const innerIframes = iframe.contentDocument?.querySelectorAll('iframe');
        if (!innerIframes?.length) return true;

        return Array.from(innerIframes).every((inner) => {
          try {
            return inner.contentWindow?.document?.readyState === 'complete';
          } catch {
            return false;
          }
        });
      } catch {
        return false;
      }
    };

    const observer = new MutationObserver(() => {
      if (checkReady()) {
        setIsLoading(false);
        observer.disconnect();
        clearInterval(intervalId);
      }
    });

    observer.observe(iframe.contentDocument.body, {
      childList: true,
      subtree: true,
    });

    const intervalId = setInterval(() => {
      if (checkReady()) {
        setIsLoading(false);
        observer.disconnect();
        clearInterval(intervalId);
      }
    }, 500);

    setTimeout(() => {
      observer.disconnect();
      clearInterval(intervalId);
      setIsLoading(false);
    }, 5000);
  };

  useEffect(() => {
    setIsLoading(true);
  }, [artifactFiles]);

  if (Object.keys(artifactFiles).length === 0) {
    return null;
  }

  return (
    <SandpackProvider
      files={{
        ...artifactFiles,
        ...sharedFiles,
      }}
      options={options}
      {...sharedProps}
      template={template}
    >
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/70 backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><circle cx="12" cy="2" r="0" fill="#000"><animate attributeName="r" begin="0" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(45 12 12)"><animate attributeName="r" begin="0.125s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(90 12 12)"><animate attributeName="r" begin="0.25s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(135 12 12)"><animate attributeName="r" begin="0.375s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(180 12 12)"><animate attributeName="r" begin="0.5s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(225 12 12)"><animate attributeName="r" begin="0.625s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(270 12 12)"><animate attributeName="r" begin="0.75s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle><circle cx="12" cy="2" r="0" fill="#000" transform="rotate(315 12 12)"><animate attributeName="r" begin="0.875s" calcMode="spline" dur="1s" keySplines="0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8;0.2 0.2 0.4 0.8" repeatCount="indefinite" values="0;2;0;0" /></circle></svg>
            <span className="text-gray-600 text-lg font-medium">Loading...</span>
          </div>
        </div>
      )}
      <SandpackPreview
        showOpenInCodeSandbox={false}
        showRefreshButton={false}
        tabIndex={0}
        ref={previewRef}
        onLoad={handleIframeLoad}
      />
    </SandpackProvider>
  );
});
