const { Client } = require('~/models');
const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');
const { logger } = require('~/config');
const { registerExternalUser } = require('~/server/services/AuthService');

const JobSimulationPublicController = {

  async getJobSimulations(req, res) {
    try {
      const params = {
        search: req.query.search || "",
        page: +(req.query.page || 1),
        limit: +(req.query.limit || 6),
      };
      const data = await JobSimulationService.getPublicJobSimulations(params);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get list of job simulation' });
    }
  },

  async externalRegistration(req, res) {
    const authHeader = req.headers['authorization'] || '';
    const apiKey = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;

    if (!apiKey) {
      return res.status(403).json({ message: 'Missing API key' });
    }

    const client = await Client.Client.findOne({ apiKey });
    logger.info({ client }, 'client')
    if (!client) {
      return res.status(403).json({ message: 'Invalid API key' });
    }

    try {
      const response = await registerExternalUser({
        ...req.body,
        clientName: client.name,
        clientAPIKey: client.apiKey,
      });
      const { status, message, data } = response;

      res.status(status).send({ message, data });
    } catch (err) {
      logger.error('[externalRegistrationController]', err);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
};

module.exports = JobSimulationPublicController;