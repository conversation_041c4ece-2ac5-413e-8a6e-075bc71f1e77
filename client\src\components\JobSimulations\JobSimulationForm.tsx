import { useState, useEffect, useCallback } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { useNavigate, useParams } from 'react-router-dom';
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Input, Label, Textarea } from '../ui';
import { useAdminJobSimulationDetail } from '~/data-provider/JobSimulation/queries';
import _get from 'lodash/get';
import { useCreateAgentMutation, useGetAgentByIdQuery } from '~/data-provider';
import { useForm } from 'react-hook-form';
import AgentAvatar from '~/components/SidePanel/Agents/AgentAvatar'
import { useToastContext } from '~/Providers';
import { useLocalize } from '~/hooks';
import { AgentForm } from '~/common';
interface EmailItemProps {
	id: string;
	title: string;
}

function SortableEmail({ id, title }: EmailItemProps) {
	const navigate = useNavigate();
	const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });
	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			{...attributes}
			{...listeners}
			onPointerDownCapture={(e) => {
				// chặn drag nếu chỉ click để điều hướng
				e.stopPropagation();
				navigate(`/job-simulations/emails/${id}`);
			}}
			className="flex items-center gap-2 rounded border px-3 py-2 shadow-sm bg-white cursor-pointer hover:bg-gray-50"
		>
			<div className="w-4 h-4 rounded-full bg-gray-400" />
			<div className="flex-1 text-sm text-left text-gray-800 truncate">{title}</div>
		</div>
	);
}

export default function JobSimulationForm() {
	const { jobId } = useParams();
	const navigate = useNavigate();
	const localize = useLocalize();

	const [showPassword, setShowPassword] = useState(false);
	const [isAgentModalOpen, setIsAgentModalOpen] = useState(false);
	const { data: jobSimulation, isLoading } = useAdminJobSimulationDetail(jobId || '');
	const agentQuery = useGetAgentByIdQuery(_get(jobSimulation, 'agentId', ''), {
		enabled: !!(_get(jobSimulation, 'agentId', '')),
	});
	const { showToast } = useToastContext();

	const [emails, setEmails] = useState<{ id: string; title: string }[]>([
		{ id: '1', title: 'email title 1' },
		{ id: '2', title: 'email title 2' },
		{ id: '3', title: 'email title ...' },
	]);

	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm({
		defaultValues: {
			name: '',
			jobSimulationId: '',
			companyName: '',
			billionIntakeCode: '',
			credential: {
				username: '',
				password: '',
			},
			agent: {
				name: '',
			}
		},
	});

	const handleDragEnd = (event: any) => {
		const { active, over } = event;
		if (active.id !== over.id) {
			const oldIndex = emails.findIndex((e) => e.id === active.id);
			const newIndex = emails.findIndex((e) => e.id === over.id);
			setEmails((emails) => arrayMove(emails, oldIndex, newIndex));
		}
	};

	const create = useCreateAgentMutation({
		onSuccess: (data) => {
			showToast({
				message: `${localize('com_assistants_create_success')} ${data.name ?? localize('com_ui_agent')
					}`,
			});
		},
		onError: (err) => {
			const error = err as Error;
			showToast({
				message: `${localize('com_agents_create_error')}${error.message ? ` ${localize('com_ui_error')}: ${error.message}` : ''
					}`,
				status: 'error',
			});
		},
	});

	const onCreateAgent = useCallback(
		(data: AgentForm) => {
			const {
				name,
				artifacts,
				description,
				instructions,
				model_parameters,
				agent_ids,
				end_after_tools,
				hide_sequential_outputs,
				recursion_limit,
			} = data;

			create.mutate({
				name,
				artifacts,
				description,
				instructions,
				model: 'o4-mini',
				provider: 'openAI',
				model_parameters,
				agent_ids,
				end_after_tools,
				hide_sequential_outputs,
				recursion_limit,
			});
		},
		[_get(jobSimulation, 'agentId', ''), create, showToast, localize],
	);

	const onSubmit = async (data) => {
		const { agent } = data;
		// const createdAgent = await new Promise((resolve, reject) => {
		// // 	create.mutate(
		// // 		{
		// // 			name: agent.name,
		// // 			instructions: '',
		// // 			artifacts: [],
		// // 			model: 'o4-mini',
		// // 			provider: 'openAI',
		// // 			model_parameters: {},
		// // 			agent_ids: [],
		// // 			end_after_tools: false,
		// // 			hide_sequential_outputs: false,
		// // 			recursion_limit: 0,
		// // 		},
		// // 		{
		// // 			onSuccess: resolve,
		// // 			onError: reject,
		// // 		},
		// // 	);
		// // });

		console.log('Form submitted:', { ...data, emails: [] });
	};

	useEffect(() => {
		if (jobSimulation) {
			reset({
				name: _get(jobSimulation, 'name', ''),
				jobSimulationId: _get(jobSimulation, 'jobSimulationId', ''),
				companyName: _get(jobSimulation, 'companyName', ''),
				billionIntakeCode: _get(jobSimulation, 'billionIntakeCode', ''),
				credential: {
					username: _get(jobSimulation, 'credential.username', ''),
					password: _get(jobSimulation, 'credential.password', ''),
				},
				agent: {
					name: agentQuery?.data?.name ?? '',
				}
			});
		}
	}, [jobSimulation, reset]);

	return (
		<>
			<form onSubmit={handleSubmit(onSubmit)}>
				<div className="max-w-3xl mx-auto space-y-6 p-6">
					<Button onClick={() => navigate(-1)}>← Back</Button>
					<div className='space-y-2'>
						<Label>Name</Label>
						<Input {...register('name', { required: 'Name is required' })} />
						{errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
					</div>
					<div className='space-y-2'>
						<Label>ID</Label>
						<Input {...register('jobSimulationId', { required: 'Job SimulationId is required' })} />
						{errors.jobSimulationId && <p className="text-red-500 text-sm">{errors.jobSimulationId.message}</p>}
					</div>
					<div className='space-y-2'>
						<Label>Company name</Label>
						<Input {...register('companyName', { required: 'Company Name is required' })} />
						{errors.companyName && <p className="text-red-500 text-sm">{errors.companyName.message}</p>}
					</div>
					<div className='space-y-2'>
						<Label>Billion Intake Code</Label>
						<Input {...register('billionIntakeCode', { required: 'Billion Intake Code is required' })} />
						{errors.billionIntakeCode && <p className="text-red-500 text-sm">{errors.billionIntakeCode.message}</p>}
					</div>
					<div className='space-y-2'>
						<Label>Credentials</Label>
						<div className="flex gap-4">
							<Input {...register('credential.username', { required: 'Username is required' })} />
							<div className="relative w-full">
								<Input type={showPassword ? 'text' : 'password'} {...register('credential.password', { required: 'Password is required' })} />
								<button
									type="button"
									onClick={() => setShowPassword((prev) => !prev)}
									className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
								>
									{showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
								</button>
							</div>
						</div>
					</div>
					<div className='space-y-2'>
						<Label>Agent</Label>
						<div
							onClick={() => setIsAgentModalOpen(true)}
							className="flex items-center gap-4 cursor-pointer hover:opacity-80"
						>
							<AgentAvatar
								className="!w-fit !justify-start"
								agent_id={agentQuery?.data?.id ?? null}
								createMutation={create}
								avatar={agentQuery?.data?.avatar ?? null}
							/>
							<div className='w-full space-y-2'>
								<Input
									{...register('agent.name', { required: 'Agent name is required' })}
								/>
								{errors.agent?.name && (
									<p className="text-red-500 text-sm">{errors.agent.name.message}</p>
								)}
							</div>
						</div>
					</div>
					{emails.length > 0 && (
						<div className='space-y-2'>
							<Label>Emails</Label>
							<DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
								<SortableContext items={emails} strategy={verticalListSortingStrategy}>
									<div className="space-y-2">
										{emails.map((email) => (
											<SortableEmail key={_get(email, 'id', '')} id={_get(email, 'id', '')} title={_get(email, 'title', '')} />
										))}
									</div>
								</SortableContext>
							</DndContext>
						</div>
					)}
					<div className="text-right">
						<Button
							type='submit'
						>
							Save
						</Button>
					</div>
				</div>
			</form>
		</>
	);
}
