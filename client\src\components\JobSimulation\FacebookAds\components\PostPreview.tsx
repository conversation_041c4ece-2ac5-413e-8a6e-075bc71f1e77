import React from 'react';
import { More<PERSON><PERSON><PERSON><PERSON>, ThumbsUp, MessageCircle, Share2 } from 'lucide-react';

interface PostPreviewProps {
	postText: string;
	selectedImages: File[];
	selectedAudience: string;
}

const PostPreview: React.FC<PostPreviewProps> = ({
	postText,
	selectedImages,
	selectedAudience,
}) => {
	return (
		<div className='w-full'>
			<p className="text-base font-medium mb-4">Preview on Facebook Feed</p>
			<div>
				<div className="bg-white rounded-xl drop-shadow-xl">
					{/* Post Header */}
					<div className="p-4 flex items-center space-x-3">
						<div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
							H
						</div>
						<div className="flex-1">
							<p className="text-base font-medium">Groups for people over 12</p>
							<p className="text-xs text-[#929294]">Just now</p>
						</div>
					</div>

					{/* Post Content */}
					{postText ? (
						<div className="px-4 pb-3">
							<p className="text-gray-900 whitespace-pre-wrap">{postText}</p>
						</div>)
						:
						<div className="space-y-3 px-4 mb-4 bg-white">
							<div className="h-4 rounded-full bg-gray-100 w-full animate-pulse"></div>
							<div className="h-4 rounded-full bg-gray-100 w-1/2 animate-pulse"></div>
						</div>}

					{/* Post Images */}
					{selectedImages.length > 0 ? (
						<div className="relative">
							{selectedImages.length === 1 ? (
								<img
									src={URL.createObjectURL(selectedImages[0])}
									alt="Post content"
									className="w-full max-h-96 object-cover"
								/>
							) : (
								<div className="grid grid-cols-2 gap-1">
									{selectedImages.slice(0, 4).map((image, index) => (
										<div key={index} className="relative">
											<img
												src={URL.createObjectURL(image)}
												alt={`Post content ${index + 1}`}
												className="w-full h-48 object-cover"
											/>
											{index === 3 && selectedImages.length > 4 && (
												<div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
													<span className="text-white text-2xl font-semibold">
														+{selectedImages.length - 4}
													</span>
												</div>
											)}
										</div>
									))}
								</div>
							)}
						</div>
					) : <div className="h-[384px] bg-gray-100 w-full animate-pulse"></div>}

					{/* Post Actions */}
					<div className="p-4">
						<div className="grid grid-cols-3 gap-1 text-base">
							<div className="flex items-center justify-center space-x-2">
								<ThumbsUp className="w-5 h-5 text-gray-500" />
								<span className="text-gray-700 font-medium">Like</span>
							</div>
							<div className="flex items-center justify-center space-x-2">
								<MessageCircle className="w-5 h-5 text-gray-500" />
								<span className="text-gray-700 font-medium">Comment</span>
							</div>
							<div className="flex items-center justify-center space-x-2">
								<Share2 className="w-5 h-5 text-gray-500" />
								<span className="text-gray-700 font-medium">Share</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PostPreview;