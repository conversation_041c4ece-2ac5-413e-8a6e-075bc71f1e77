import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger, RadioGroup, RadioGroupItem } from "~/components/ui";

interface SharingFrequencySelectorProps {
	value?: string;
	onValueChange?: (value: string) => void;
}

const SharingFrequencySelector = ({
	value = 'share-once',
	onValueChange
}: SharingFrequencySelectorProps) => {
	const [open, setOpen] = useState(false);
	const [selectedValue, setSelectedValue] = useState(value);

	const options = [
		{
			id: 'share-always',
			title: 'Share always',
			description: 'Automatically share this and all future posts to your story. This setting will remain on for future posts unless you turn it off.'
		},
		{
			id: 'share-once',
			title: 'Share once',
			description: 'Only share this post to your story.'
		}
	];

	const handleValueChange = (newValue: string) => {
		setSelectedValue(newValue);
		onValueChange?.(newValue);
		setOpen(false);
	};

	const selectedOption = options.find(option => option.id === selectedValue);

	return (
		<div className="w-full">
			<h2 className="text-base font-medium mb-2">Set sharing frequency</h2>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<button className="w-full p-2 border rounded-lg bg-white text-sm">
						<div className="flex items-center justify-between">
							<span className="text-left font-normal">
								{selectedOption?.title || 'Select option'}
							</span>
							<ChevronDown className="w-5 h-5 text-gray-400" />
						</div>
					</button>
				</PopoverTrigger>

				<PopoverContent className="w-full p-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50" align="start">
					<div className="p-1">
						<RadioGroup
							value={selectedValue}
							onValueChange={handleValueChange}
							className="gap-0"
						>
							{options.map((option) => (
								<label
									key={option.id}
									className="flex items-start space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors"
								>
									<RadioGroupItem
										value={option.id}
										className="mt-1 flex-shrink-0"
									/>
									<div className="flex-1">
										<div className="text-base font-normal text-gray-900">
											{option.title}
										</div>
										<div className="text-sm text-gray-600 leading-relaxed">
											{option.description}
										</div>
									</div>
								</label>
							))}
						</RadioGroup>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
};

export default SharingFrequencySelector;