import React from 'react';
import { motion } from 'framer-motion';

export default function SiriGlowEffect({ wrapperStyle, glowingWidth = 3, style, chatWidth, isTogglingRef, ref }: { wrapperStyle?: string, glowingWidth?: number, style?: React.CSSProperties, chatWidth?: number, isTogglingRef?: any, ref?: any }) {
    return (
        <motion.div
            className={`glowing-wrapper ${wrapperStyle}`}
            style={{
                ['--rotationDuration' as any]: '2134ms',
                ['--glowingWidth' as any]: `${glowingWidth}px`,
                ['--glowingBlurRatio' as any]: 1,
                ...style
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1, width: chatWidth + '%' }}
            exit={{ opacity: 0 }}
            transition={
                isTogglingRef?.current
                    ? { type: 'spring', stiffness: 150, damping: 25 }
                    : { duration: 0.6, ease: 'easeInOut' }
            }
            ref={ref}
        >
            <div className="glowing-container">
                <div className="glowing-container--blur-wrapper">
                    <div className="glowing-border"></div>
                </div>
            </div>
            <div className="glowing-container glowing-container-2">
                <div className="glowing-container--blur-wrapper">
                    <div className="glowing-border glowing-border2"></div>
                </div>
            </div>
            <div className="glowing-container glowing-container-3">
                <div className="glowing-container--blur-wrapper">
                    <div className="glowing-border glowing-border3"></div>
                </div>
            </div>
        </motion.div>
    );
}
