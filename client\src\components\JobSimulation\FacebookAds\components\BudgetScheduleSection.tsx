import React, { useState } from 'react';
import { CalendarIcon, Info, Edit, ChevronUp, XCircleIcon, PenIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Button, Calendar, Card, CardContent, CardHeader, CardTitle, Popover, PopoverContent, PopoverTrigger } from '~/components/ui';
import { Switch } from '~/components/ui';
import AudienceSelection from './AudienceSelection';

const BudgetScheduleSection = () => {
	const [advantagePlusEnabled, setAdvantagePlusEnabled] = useState(true);
	const [specialCategoryEnabled, setSpecialCategoryEnabled] = useState(false);
	const [startDate, setStartDate] = useState<Date>(new Date(2025, 4, 29)); // May 29, 2025
	const [endDate, setEndDate] = useState<Date>();
	const [budget, setBudget] = useState(182567);

	return (
		<div className="space-y-6">
			{/* Advantage+ Content */}
			<div className='bg-white rounded-lg drop-shadow-lg p-4'>
				<div className="flex items-center justify-between mb-4">
					<span className='text-lg font-medium'>Advantage+ Content</span>
					<Info className="w-4 h-4" />
				</div>
				<div className='flex gap-4 justify-between'>
					<p className="text-sm text-gray-600">
						Leverage Facebook’s data to automatically deliver different ad creative variations to people when likely to improve performance.
					</p>
					<Switch
						checked={advantagePlusEnabled}
						onCheckedChange={setAdvantagePlusEnabled}
					/>
				</div>
			</div>

			{/* Special Ad Category */}
			<div className='bg-white rounded-lg drop-shadow-lg p-4'>
				<div className="flex items-center justify-between mb-4">
					<span className='text-lg font-medium'>Special Ad Category</span>
					<Info className="w-4 h-4" />
				</div>
				<div className='flex gap-4 justify-between'>
					<p className="text-sm text-gray-600">
						Ads about financial products and services, employment, housing, or social issues, elections or politics
					</p>
					<Switch
						checked={specialCategoryEnabled}
						onCheckedChange={setSpecialCategoryEnabled}
					/>
				</div>
			</div>

			{/* Audience */}
			<AudienceSelection />

			{/* Budget and Schedule */}
			<div className='bg-white rounded-lg drop-shadow-lg p-4'>
				<div className="flex items-center justify-between mb-4">
					<span className='text-lg font-medium'>Schedule and budget</span>
					<Info className="w-4 h-4" />
				</div>
				<div className="grid grid-cols-2 gap-4 mb-3">
					<div>
						<label className="text-sm font-medium mb-2">Start Date</label>
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="outline" className="w-full justify-start text-left">
									<CalendarIcon className="mr-2 h-4 w-4" />
									{startDate ? format(startDate, "MMM d, yyyy") : "Select date"}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								Calendar
								{/* <Calendar
										mode="single"
										selected={startDate}
										onSelect={setStartDate}
										initialFocus
									/> */}
							</PopoverContent>
						</Popover>
					</div>

					<div>
						<label className="text-sm font-medium mb-2">Start Time</label>
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="outline" className="w-full justify-start text-left">
									<CalendarIcon className="mr-2 h-4 w-4" />
									{startDate ? format(startDate, "MMM d, yyyy") : "Select date"}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								Calendar
								{/* <Calendar
										mode="single"
										selected={startDate}
										onSelect={setStartDate}
										initialFocus
									/> */}
							</PopoverContent>
						</Popover>
					</div>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<div>
						<label className="text-sm font-medium mb-2">Number of Days</label>
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="outline" className="w-full justify-start text-left">
									<CalendarIcon className="mr-2 h-4 w-4" />
									{endDate ? format(endDate, "MMM d, yyyy") : "Jun 5, 2025"}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								Calendar
							</PopoverContent>
						</Popover>
					</div>

					<div>
						<label className="text-sm font-mediummb-2">End Date</label>
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="outline" className="w-full justify-start text-left">
									<CalendarIcon className="mr-2 h-4 w-4" />
									{endDate ? format(endDate, "MMM d, yyyy") : "Jun 5, 2025"}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								Calendar
							</PopoverContent>
						</Popover>
					</div>
				</div>

				<p className="text-sm text-gray-600 mt-3">
					Your ads will run for 7 days, from today until June 5, 2025.
				</p>

				{/* Budget */}
				<div>
					<div className="flex items-center justify-between my-4">
						<span className='text-lg font-medium'>Daily budget</span>
						<Info className="w-4 h-4" />
					</div>
					<div className="border-l-4 border-green-500 bg-white drop-shadow p-4 rounded-lg mb-4">
						<div className="flex items-center justify-between mb-2">
							<div className='flex items-center gap-3'>
								<Info className="w-5 h-5 text-green-600 mt-0.5" />
								<h4 className="text-sm font-medium">
									Introducing daily budget for boosting
								</h4>
							</div>
							<XCircleIcon className="w-6 h-6" />
						</div>
						<p className="text-sm">
							Get more control over your ad spend by setting the average amount you want to spend each day on your boosted content. <span className='text-blue-400 cursor-pointer hover:underline'>More about daily budget</span>
						</p>
					</div>
					<p className="text-base mb-4 text-center">
						Estimated 4.6K - 13.2K <span className='text-blue-500 font-medium'>Accounts Center accounts</span> reached per day
					</p>

					<div className="text-center my-6">
						<div className="text-5xl font-bold text-blue-600 flex items-center justify-center">
							<span className="text-base mr-2">₫</span>
							<span>
								{budget.toLocaleString()}
							</span>
							<PenIcon className="w-6 h-6 ml-2 cursor-pointer" />
						</div>
					</div>

					<div className="relative flex items-center gap-6">
						<span className='text-xs text-[#929294]'>{budget.toLocaleString()} ₫</span>
						<div className="w-full bg-gray-200 rounded-full h-2 flex-1">
							<div
								className="bg-blue-600 h-2 rounded-full"
								style={{ width: `${(budget / 2000000) * 100}%` }}
							></div>
						</div>
						<span className='text-xs text-[#929294]'>2,000,000 ₫</span>
					</div>
				</div>
			</div>

			<div className='bg-white rounded-lg drop-shadow-lg p-4'>
				<div className="flex items-center justify-between mb-4">
					<span className='text-lg font-medium'>Placements</span>
					<Info className="w-4 h-4" />
				</div>
				<div className='flex gap-4 justify-between'>
					<div>
						<p className='text-xs text-green-500 font-normal'>Recommended</p>
						<div className='flex items-center gap-1'>
							<p className='text-sm font-normal'>Advantage+ placements</p>
							<Info className="w-4 h-4" />
						</div>
						<p className="text-sm">
							Let us maximize your budget across Facebook, Messenger, Instagram and Meta Audience Network to help show your ad to more people.
						</p>
					</div>
					<Switch
						checked={specialCategoryEnabled}
						onCheckedChange={setSpecialCategoryEnabled}
					/>
				</div>
			</div>
		</div>
	);
};

export default BudgetScheduleSection;