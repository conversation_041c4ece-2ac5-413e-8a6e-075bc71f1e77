import { TJobSimulationEmail } from 'src/types';

const getEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
  companyName?: string;
}): TJobSimulationEmail[] => [
  {
    id: '1',
    name: 'HR Team',
    avatar: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg',
    role: 'HR Manager',
    email: '<EMAIL>',
    title: "Welcome to BrightWave Media's Digital Marketing Simulation!",
    desc: "We're thrilled to have you onboard for this immersive experience.",
    nextEmailId: '2',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `
Welcome to **BrightWave Media!** We're excited to have you join our **Digital Marketing Simulation**.

This simulation is designed to provide you with hands-on experience in digital marketing strategies, content creation, and data analysis. You'll be working on real-world tasks that reflect the dynamic nature of our industry.

To get started, please check your inbox for a meeting invitation from your manager, **<PERSON>*.

We look forward to seeing your creativity and analytical skills in action!
`,
      signature: {
        title: 'Regards',
        company: data.companyName,
      },
    },
    triggerActions: [
      {
        type: 'nextEmail',
        data: { nextEmailId: '2', triggerTimeout: 3, when: 'open' },
      },
    ],
  },
  {
    id: '2',
    name: 'Alex Chen',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Manager',
    email: '<EMAIL>',
    title: 'Kick-off Meeting: BrightWave Media Digital Marketing Simulation',
    desc: "Let's connect to discuss your role and upcoming tasks.",
    nextEmailId: '3',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `
I'm **Alex Chen**, your manager at BrightWave Media.
We're excited to kick off your Digital Marketing Simulation with an introductory meeting.
In this brief session, you'll learn about **your role and upcoming tasks**, and get set up for success.

I look forward to meeting you live and officially kicking things off together!
  `,
      actions: [
        {
          type: 'joinMeeting',
          label: 'Join the Meeting',
          title: 'Onboarding Meeting',
          data: {
            datetime: '{email_time}',
            duration: '~1 minutes',
            meetingLink: 'https://dev-bitmeet.mnet.io/introduction/642-b26-a8c?t=mi',
            // TODO: update code, listen postMessage
            triggerAssistant: 'I have completed the meeting with Alex Chen.',
            enableApps: ['mail', 'news', 'meeting', 'task-board', 'facebook-ad', 'fb-ad'],
            pushNextEmail: false,
          },
        },
      ],
      signature: {
        title: 'Warm regards',
        company: data.companyName,
      },
    },
    triggerActions: [
      // {
      //   type: 'triggerAssistant',
      //   data: { triggerTimeout: 1, triggerMessage: "I've received the meeting invitation." },
      // },
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: "I've received the meeting invitation.",
          when: 'receive',
        },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'facebook-ad', 'fb-ad'],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '3',
    name: ' Alex Chen',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Manager',
    email: '<EMAIL>',
    title: 'Thank You for Joining the Digital Marketing Simulation',
    desc: 'Congratulations on completing the simulation!',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `
Congratulations on successfully completing the Digital Marketing Simulation at BrightWave Media!
Your efforts in content planning and data analysis have been impressive.

As a token of our appreciation, we've prepared a reference letter highlighting your contributions and skills demonstrated during the simulation.

You can view your reference letter here:
  `,
      actions: [
        {
          type: 'viewFileDetail',
          label: 'View Reference Letter',
          title: 'Reference Letter',
          data: {
            fileUrl: `https://uat.internship.guru/en/public/reference-letter?programId=${data.billionIntakeId}&autoClaim=true`,
          },
        },
      ],
      signature: {
        title: 'regards',
        company: data.companyName,
      },
    },
  },
];

export default getEmails;
