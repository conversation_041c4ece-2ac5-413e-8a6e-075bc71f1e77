import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, HelpCircle, MoreHorizontal, TrendingUp } from 'lucide-react';
import { Button } from '~/components/ui';

const AdAnalytics = ({ onGoBackAdList }) => {
	const { id } = useParams();
	const navigate = useNavigate();

	return (
		<div>
			{/* Header */}
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center">
					<Button
						variant="ghost"
						onClick={onGoBackAdList}
						className="flex items-center space-x-2"
					>
						<ArrowLeft className="w-4 h-4" />
					</Button>
					<div className="flex items-center space-x-2">
						<div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
							W
						</div>
						<div>
							<h1 className="text-lg font-medium">Hello Everyone</h1>
							<div className="flex items-center space-x-2 text-sm text-gray-500">
								<span>Post • Posted May 29 at 4:07 PM</span>
							</div>
						</div>
					</div>
				</div>
				<Button className="bg-blue-500 hover:bg-blue-600 text-white rounded">
					Boost
				</Button>
			</div>

			<div className="grid grid-cols-12 gap-6">
				{/* Left Column - Analytics */}
				<div className="col-span-8 space-y-6">
					{/* Overview Section */}
					<div className='bg-white rounded drop-shadow-lg p-4'>
						<p className="text-lg font-medium mb-4">Overview</p>
						<div className="grid grid-cols-4 gap-6">
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Views</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Reach</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Interactions</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Interactions</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">--</div>
							</div>
						</div>
					</div>

					{/* Views Chart Section */}
					<div className='bg-white rounded drop-shadow-lg p-4'>
						<div className="flex items-center space-x-2">
							<p className="text-lg font-medium">Views</p>
							<HelpCircle className="w-4 h-4 text-gray-400" />
						</div>
						<div>
							<div className="text-3xl font-mediummb-4">0</div>

							{/* Empty State Chart */}
							<div className="h-64 flex flex-col items-center justify-center">
								<TrendingUp className="w-12 h-12 text-gray-400 mb-4" />
								<div className="text-center">
									<p className="text-lg font-normal">No activity during selected date range</p>
									<p className="text-sm text-[#929294]">Please select a different date range and try to load your report again.</p>
								</div>
							</div>

							{/* Audience Demographics */}
							<div className="mt-6 pt-6 border-t border-gray-200">
								<div>
									<p className="text-base font-normal">From followers</p>
									<p className="border-l border-l-2 border-blue-500 text-sm text-gray-600 pl-2">0%</p>
								</div>
								<div>
									<p className="text-base font-normal">From non-followers</p>
									<p className="border-l border-l-2 border-blue-500 text-sm text-gray-600 pl-2">0%</p>
								</div>
							</div>
						</div>
					</div>

					{/* Interactions Section */}
					<div className='bg-white rounded drop-shadow-lg p-4'>
						<p className="text-lg font-medium mb-4">Interactions</p>
						<div className="grid grid-cols-4 gap-6">
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Reactions</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Comments</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Shares</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">0</div>
							</div>
							<div>
								<div className="flex items-center space-x-1 mb-2">
									<span className="text-sm font-medium">Saves</span>
									<HelpCircle className="w-4 h-4" />
								</div>
								<div className="text-3xl font-medilum">--</div>
							</div>
						</div>
					</div>
				</div>

				{/* Right Column - Preview */}
				<div className="col-span-4">
					<div className='bg-white drop-shadow-lg p-4 rounded'>
						<p className="text-lg font-medium mb-4">Feed preview</p>
						{/* Facebook Post Preview */}
						<div className="bg-gray-50 rounded px-4 py-2">
							{/* Post Header */}
							<div className='p-3 bg-white rounded-tl-lg rounded-tr-lg'>
								<div className="flex items-center space-x-3">
									<div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
										H
									</div>
									<div className="flex-1">
										<div className="flex items-center space-x-2">
											<span className="font-medium text-sm">Name post</span>
										</div>
										<div className="text-xs text-gray-500">18 minutes • 🌍</div>
									</div>
									<MoreHorizontal />
								</div>
								<p className="text-sm my-3">Hello Everyone</p>
							</div>
							{/* Post Image */}
							<div className='bg-gray-200 w-full h-[500px]' />
							<div className='p-2 bg-white rounded-bl-lg rounded-br-lg space-y-3'>
								<Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white text-xs px-3 py-1 rounded">
									Boost
								</Button>
								{/* Post Actions */}
								<div className="p-3 border-t border-gray-100">
									<div className="flex justify-between text-sm text-gray-500 mb-3">
										<button className="flex items-center space-x-2">
											<span>👍</span>
											<span>Like</span>
										</button>
										<button className="flex items-center space-x-2">
											<span>💬</span>
											<span>Comment</span>
										</button>
										<button className="flex items-center space-x-2">
											<span>📤</span>
											<span>Share</span>
										</button>
									</div>
									<hr />
									{/* Comment Section */}
									<div className="flex items-center space-x-2 text-sm mt-3">
										<div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
											H
										</div>
										<div className="flex-1 bg-gray-100 rounded-full px-3 py-2">
											<span className="text-gray-500">Write a comment below Ask the people in...</span>
										</div>
									</div>
								</div>
							</div>
							<Button variant="outline" className="text-sm font-normal flex items-center space-x-1 mt-4 w-full bg-transparent">
								<span>📱</span>
								<span>View post on Facebook</span>
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div >
	);
};

export default AdAnalytics;