const AgentosExternalService = require('../services/AgentosExternalService');

const AgentosExternalController = {
	async getAgents(req, res) {
		// console.log('=== API HIT: GET /api/external/agents ===');
		try {
			const { projectId, page, size } = req.query;
			const data = await AgentosExternalService.fetchAgents({ projectId, page, size });
			// console.log('=== DATA FROM 3RD API ===', data);
			// console.log('data from 3rd api:', data);
			res.json(data);
		} catch (error) {
			console.error(error);
			console.error('=== ERROR ===', error.response?.data || error.message);
			res.status(500).json({ error: 'Failed to fetch agents from Agentos Cloud' });
		}
	},
};

module.exports = AgentosExternalController;