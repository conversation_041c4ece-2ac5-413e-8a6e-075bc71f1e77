const express = require('express');
const router = express.Router();
const JobSimulationController = require('~/server/controllers/JobSimulationController');
const { requireJwtAuth } = require('~/server/middleware');

router.use(requireJwtAuth);

router.post('/:jobSimulationId', JobSimulationController.getOrCreateProgress);
router.put('/:jobSimulationId/update', JobSimulationController.updateProgress);

module.exports = router;
