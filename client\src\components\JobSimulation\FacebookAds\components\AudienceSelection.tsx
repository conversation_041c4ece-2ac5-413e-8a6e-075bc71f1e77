import React, { useState } from 'react';
import { ChevronUp, Edit, Info } from 'lucide-react';
import { Button, RadioGroup, RadioGroupItem } from '~/components/ui';

interface AudienceSelectionProps {
	onCreateNew?: () => void;
	onLearnMore?: () => void;
}

const AudienceSelection = ({ onCreateNew, onLearnMore }: AudienceSelectionProps) => {
	const [selectedOption, setSelectedOption] = useState('advantage');

	return (
		<div className="bg-white rounded-lg drop-shadow-lg p-4">
			{/* Header */}
			<div className="mb-4">
				<div className='flex items-center justify-between'>
					<p className="text-lg font-medium">Audience</p>
					<Info className="w-5 h-5 text-gray-400" />
				</div>
				<p className="text-sm">Who should see your ad?</p>
			</div>

			{/* Radio Group */}
			<RadioGroup
				value={selectedOption}
				onValueChange={setSelectedOption}
				className="space-y-4"
			>
				<div className='space-y-3'>
					{/* Advantage+ audience option */}
					<label className="flex items-start space-x-4 cursor-pointer">
						<RadioGroupItem
							value="advantage"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-base">
								Advantage+ audience
							</div>
							<div className="text-[#929294] text-xs">
								Let our ad technology automatically find your audience and adjust over time to reach more people who are likely to respond to your ad. Learn more{' '}
								<button className="text-blue-600 hover:text-blue-800 underline">
									Learn more
								</button>
							</div>
						</div>
					</label>
					{/* Show advantage+ details when selected */}
					{selectedOption === 'advantage' && (
						<div className="bg-gray-50 rounded-lg p-4 space-y-3">
							<div className="flex items-center justify-between">
								<h4 className="text-base font-normal">Audience details</h4>
								<Edit className="w-4 h-4 text-gray-600 cursor-pointer" />
							</div>
							<div className="space-y-2 text-sm">
								<div>
									<span className="text-[#929294]">Location: </span>
									<span>Vietnam</span>
								</div>
								<div>
									<span className="text-[#929294]">Minimum age: </span>
									<span>18</span>
								</div>
								<div>
									<span className="text-[#929294]">Advantage+ audience: </span>
									<span>On</span>
								</div>
							</div>
						</div>
					)}
				</div>

				{/* People you choose through targeting option */}
				<div className='space-y-3'>
					<label className="flex items-start space-x-4 cursor-pointer">
						<RadioGroupItem
							value="targeting"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-base font-normal">
								People you choose through targeting
							</div>
						</div>
					</label>
					{/* Show targeting details when selected */}
					{selectedOption === 'targeting' && (
						<div className="space-y-4">
							<div className="bg-gray-50 rounded-lg p-4 space-y-3">
								<div className="flex items-center justify-between">
									<h4 className="text-base font-normal">Audience details</h4>
									<Edit className="w-4 h-4 text-gray-600 cursor-pointer" />
								</div>
								<div className="space-y-2 text-sm">
									<div>
										<span className="text-[#929294]">Location: </span>
										<span>Vietnam</span>
									</div>
									<div>
										<span className="text-[#929294]">Age: </span>
										<span>18 - 65+</span>
									</div>
									<div>
										<span className="text-[#929294]">Advantage+ detailed targeting: </span>
										<span>On</span>
									</div>
									<div>
										<span className="text-[#929294]">Advantage+ audience: </span>
										<span>Off</span>
									</div>
								</div>
							</div>

							{/* Improvements section */}
							<div className="border-l-4 border-blue-500 bg-white drop-shadow p-4 rounded-lg">
								<div className="flex items-center justify-between mb-2">
									<div className='flex items-center gap-3'>
										<Info className="w-5 h-5 text-blue-600 mt-0.5" />
										<h4 className="text-sm font-medium">
											Improvements to ad delivery
										</h4>
									</div>
									<ChevronUp className="w-4 h-4 text-gray-400" />
								</div>
								<p className="text-sm mb-4">
									We may deliver ads beyond your lookalike audiences and detailed targeting selections for your selected objective, if it’s likely to improve performance.
								</p>
								<Button
									variant="outline"
									className="w-full font-normal"
									onClick={onLearnMore}
								>
									Learn more
								</Button>
							</div>
						</div>
					)}
				</div>
			</RadioGroup>

			{/* Create new button */}
			<div className="pt-4">
				<Button
					variant="outline"
					className="w-full py-3 font-normal"
					onClick={onCreateNew}
				>
					Create new
				</Button>
			</div>
		</div>
	);
};

export default AudienceSelection;