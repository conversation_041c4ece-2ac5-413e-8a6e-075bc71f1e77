import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, Info, Calendar, CheckCircle } from 'lucide-react';
import { Avatar, Button } from '~/components/ui';

const AdOverview = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const [showExpanded, setShowExpanded] = useState(false);

	// Determine which tab is active based on current route
	const isAdSummaryActive = location.pathname === '/ad-overview';
	const isAllAdsActive = location.pathname === '/ads';

	const handleAdSummaryClick = () => {
		setShowExpanded(true);
		navigate('/ad-overview');
	};

	const handleAllAdsClick = () => {
		navigate('/ads');
	};

	const handleAudiencesClick = () => {
		// Navigate to audiences page when implemented
		console.log('Navigate to audiences');
	};

	const handleAdManagerClick = () => {
		// Navigate to ad manager page when implemented
		console.log('Navigate to ad manager');
	};

	const metrics = [
		{ label: 'Views', value: '--', hasInfo: true },
		{ label: 'Reach', value: '--', hasInfo: true },
		{ label: 'Post engagements', value: '--', hasInfo: true },
		{ label: 'Link clicks', value: '--', hasInfo: true }
	];

	return (
		<div className='h-full'>
			<div>
				<div className='space-y-5'>
					<div className='space-y-5 bg-white rounded p-4 drop-shadow-lg'>
						<div className="flex items-center justify-between">
							<div>
								<p className="text-lg font-medium">Advertising summary</p>
								<p className='text-sm'>User spent ₫0.00 on 0 ads in the last 60 days</p>
							</div>
							<div className="flex items-center space-x-2">
								<Button variant="outline" className="flex items-center space-x-1 font-normal">
									<Calendar className="w-4 h-4" />
									<span>60 days: Mar 31, 2025 - May 29, 2025</span>
									<ChevronDown className="w-4 h-4" />
								</Button>
							</div>
						</div>

						{/* Metrics Grid */}
						<div className="grid grid-cols-4 gap-4 mb-8">
							{metrics.map((metric, index) => (
								<div key={index} className="bg-white rounded-lg p-4 border">
									<div className="flex items-center space-x-2 mb-2">
										<span className="text-base font-medium">{metric.label}</span>
										{metric.hasInfo && <Info className="w-4 h-4" />}
									</div>
									<div className="text-2xl font-bold mb-2">{metric.value}</div>
									<Button variant="outline" size="sm" className="w-full">
										See more
									</Button>
								</div>
							))}
						</div>
					</div>
					{/* Block */}
					<div className="grid grid-cols-12 gap-4">
						{/* Empty State and Audiences Section */}
						<div className="col-span-8 space-y-5">
							{/* Empty State */}
							<div className="bg-white rounded-lg p-8 text-center drop-shadow-lg">
								<div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
									<div className="w-12 h-12 bg-blue-200 rounded flex items-center justify-center">
										📊
									</div>
								</div>
								<h3 className="text-lg font-medium">View results</h3>
								<p className="text-base">
									After you start, you'll see data from your ads from the last 5 days.
								</p>
							</div>
							{/* Audiences Section */}
							<div>
								<div className="bg-white rounded-lg p-4 text-center drop-shadow-lg">
									<div className="flex items-center justify-between mb-4">
										<h2 className="text-lg font-medium">Audiences</h2>
										<Button variant="outline" className="flex items-center space-x-1">
											<span className='font-normal'>Last 30 days</span>
											<ChevronDown className="w-4 h-4" />
										</Button>
									</div>
									<div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
										<div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
											👥
										</div>
									</div>
									<p className="text-base font-semibold mb-4">
										You’ll see audience insights here when 100{' '}
										<span className="text-blue-600 cursor-pointer">Accounts Center accounts</span>{' '}
										engage with your ads
									</p>
									<p className="max-w-2xl text-base mb-6 mx-auto">
										Post engagement audience insights can help you learn about the people taking action on your ads with a breakdown of age and gender, placements and locations.
									</p>
									<Button variant="outline" className='font-normal'>Create Ad</Button>
								</div>
							</div>
						</div>
						{/* Hello Section */}
						<div className="col-span-4 space-y-5">
							<div className="bg-white rounded-lg p-4 drop-shadow-lg">
								<p className='text-base font-medium mb-4'>Ad drafts</p>
								<div className='flex items-center gap-4'>
									<Avatar className='bg-black w-14 h-14' />
									<div className='flex-1 space-y-1'>
										<p className='text-xs text-[#929294]'>Expires in 28 days</p>
										<p className='text-sm font-light'>Draft: Get more messages</p>
										<p className='text-xs text-[#929294]'>Last updated May 29, 2025, 4:08 PM</p>
									</div>
								</div>
							</div>
							<div className="bg-white rounded-lg p-4 drop-shadow-lg">
								<p className='text-base font-medium mb-4'>Recommendations</p>
								<div className='flex justify-center'>
									<div className='text-center max-w-[256px] max-auto'>
										<CheckCircle className='w-8 h-8 mx-auto text-green-500' />
										<p className='text-base font-semibold'>Completed</p>
										<p className='text-sm text-[#929294]'>Nice work! Check back soon for new personalized recommendations.</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					{/* Block */}
				</div>
			</div>
			{/* )} */}
		</div>
	);
};

export default AdOverview;