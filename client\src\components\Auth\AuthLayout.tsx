import { TranslationKeys, useLocalize } from '~/hooks';
import { motion } from 'framer-motion';
import { TStartupConfig } from 'librechat-data-provider';
import SocialLoginRender from './SocialLoginRender';
import { ThemeSelector } from '~/components/ui';
import { Banner } from '../Banners';
import Footer from './Footer';
import React from 'react';

const ErrorRender = ({ children }: { children: React.ReactNode }) => (
  <div className="mt-10 mb-10 flex justify-center">
    <div
      role="alert"
      aria-live="assertive"
      className="rounded-md border border-red-500 bg-red-500/10 px-3 py-2 text-sm text-gray-600 dark:text-gray-200"
    >
      {children}
    </div>
  </div>
);

function AuthLayout({
  children,
  header,
  headerLogo,
  isFetching,
  startupConfig,
  startupConfigError,
  pathname,
  error,
  // orderClassName,
}: {
  children: React.ReactNode;
  header: React.ReactNode;
  headerLogo?: string;
  isFetching: boolean;
  startupConfig: TStartupConfig | null | undefined;
  startupConfigError: unknown | null | undefined;
  pathname: string;
  error: TranslationKeys | null;
  // orderClassName?: string;
}) {
  const localize = useLocalize();

  const hasStartupConfigError = startupConfigError !== null && startupConfigError !== undefined;
  const DisplayError = () => {
    if (hasStartupConfigError) {
      return <ErrorRender>{localize('com_auth_error_login_server')}</ErrorRender>;
    } else if (error === 'com_auth_error_invalid_reset_token') {
      return (
        <ErrorRender>
          {localize('com_auth_error_invalid_reset_token')}{' '}
          <a className="font-semibold text-green-600 hover:underline" href="/forgot-password">
            {localize('com_auth_click_here')}
          </a>{' '}
          {localize('com_auth_to_try_again')}
        </ErrorRender>
      );
    } else if (error != null && error) {
      return <ErrorRender>{localize(error)}</ErrorRender>;
    }
    return null;
  };

  return (
    <div className="relative flex flex-col min-h-screen items-center justify-center bg-white dark:bg-gray-900 overflow-hidden px-4 sm:px-8">
      <Banner />
      <DisplayError />
      <div className="absolute bottom-0 left-0 md:m-4">
        <ThemeSelector />
      </div>

      <div className="flex w-full max-w-6xl items-center justify-between gap-12 sm:flex-row flex-col z-10">
        {/* FORM SECTION */}
        <motion.div
          initial={{ opacity: 0, x: -100, scale: 0.95 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          transition={{ type: 'spring', stiffness: 40, damping: 18, delay: 0.1 }}
          className="w-full bg-white dark:bg-gray-800 rounded-2xl shadow-2xl px-8 py-10 space-y-6 min-h-[380px]"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
            Welcome to <span className="text-yellow-400">Job Simulation</span>
          </h1>
          {children}
          {!pathname.includes('2fa') &&
            (pathname.includes('login') || pathname.includes('register')) && (
              <SocialLoginRender startupConfig={startupConfig} />
            )}
        </motion.div>

        {/* ILLUSTRATION SECTION */}
        <motion.div
          initial={{ opacity: 0, y: 60 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.8, ease: 'easeOut' }}
          className="w-full sm:max-w-2xl sm:order-last order-first"
        >
          <img
            src={headerLogo}
            alt={localize('com_ui_logo', { 0: startupConfig?.appTitle ?? 'Job Simulation' })}
            className="w-full h-auto object-contain drop-shadow-xl"
          />
        </motion.div>
      </div>
      <Footer startupConfig={startupConfig} />
    </div>
  );
}

export default AuthLayout;
