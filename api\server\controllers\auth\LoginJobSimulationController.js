const { registerJobSimulationUser } = require('~/server/services/AuthService');
const { logger } = require('~/config');

const loginJobSimulationController = async (req, res) => {
  try {
    const registrationData = {
      ...req.body,
      email: req.user.email,
      userId: req.user.id,
    };
    const names = req.user.name.split(' ');
    registrationData.firstName = names[0];
    registrationData.lastName = names[1] || '';
    const jobSimulationUser = await registerJobSimulationUser(registrationData);

    // const { password: _p, totpSecret: _t, __v, ...user } = req.user;
    // user.id = user._id.toString();

    // const token = await setAuthTokens(userData.id || userData._id.toString(), res);

    return res.status(200).send({ user: { ...jobSimulationUser, name: req.user.name } });
  } catch (err) {
    logger.error('[loginJobSimulationController]', err);
    return res.status(500).json({ message: err.message || 'Something went wrong' });
  }
};

module.exports = {
  loginJobSimulationController,
};
