import { useSetRecoilState } from 'recoil';
import type { Artifact } from '~/common';
import FilePreview from '~/components/Chat/Input/Files/FilePreview';
import { useLocalize } from '~/hooks';
import { useMessageContext } from '~/Providers/MessageContext';
import store from '~/store';
import { getFileType } from '~/utils';

const ArtifactButtonIcon = ({ artifact }: { artifact: Artifact }) => {
  const { messageIconURL } = useMessageContext();

  if (artifact.isIframe && messageIconURL) {
    return (
      <img
        src={messageIconURL}
        alt="webview"
        className="relative size-10 shrink-0 overflow-hidden rounded-xl object-cover !my-0"
      />
    );
  }

  const fileType = getFileType(artifact.isIframe ? 'webview' : 'artifact');

  return <FilePreview fileType={fileType} className="relative" />;
};

const ArtifactButton = ({ artifact }: { artifact: Artifact | null }) => {
  const localize = useLocalize();
  const setVisible = useSetRecoilState(store.artifactsVisible);
  const setArtifactId = useSetRecoilState(store.currentArtifactId);
  if (artifact === null || artifact === undefined) {
    return null;
  }

  return (
    <div className="group relative my-4 rounded-xl text-sm text-text-primary">
      <button
        type="button"
        onClick={() => {
          setArtifactId(artifact.id);
          setVisible(true);
        }}
        className="relative overflow-hidden rounded-xl border border-border-medium transition-all duration-300 hover:border-border-xheavy hover:shadow-lg"
      >
        <div className="w-fit bg-surface-tertiary p-2">
          <div className="flex flex-row items-center gap-2">
            {/* <FilePreview fileType={fileType} className="relative" /> */}
            <ArtifactButtonIcon artifact={artifact} />
            <div className="overflow-hidden text-left">
              <div className="truncate font-medium">{artifact.title}</div>
              <div className="truncate text-text-secondary">
                {localize('com_ui_artifact_click')}
              </div>
            </div>
          </div>
        </div>
      </button>
      <br />
    </div>
  );
};

export default ArtifactButton;
