const { uploadFileToS3 } = require('~/server/services/Files/S3/crud');

const { JobSimulation, JobSimulationProgress } = require('~/models');
const { getAgentByJobSimulationId } = require('~/models/Agent');

function generateSecretCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

const JobSimulationService = {

  async getUserJobSimulationsInfo(params) {
    const jobs = await JobSimulation.getUserJobSimulationsInfo(params);
    const jobIds = jobs.map((job) => job.jobSimulationId);
    const progresses = await JobSimulationProgress.getProgresses({ jobSimulationIds: jobIds, email: params.email });
    const mapObjProgressByJobId = progresses.reduce((acc, curr) => {
      acc[curr.jobSimulationId] = curr;
      return acc;
    }, {});

    jobs.forEach((job) => {
      job.progress = mapObjProgressByJobId[job.jobSimulationId];
    });

    return jobs;
  },

  async getUserJobSimulationInfo(params) {
    const { jobSimulationId, email } = params;
    // TODO: don't use this function, it return many fields
    const job = await JobSimulation.getJobSimulation(params);
    const [progress, agent] = await Promise.all([
      JobSimulationProgress.getProgress({ jobSimulationId, email }),
      getAgentByJobSimulationId(jobSimulationId),
    ])

    job.forEach((job) => {
      job.progress = progress;
      job.agent = agent
    });

    return job;
  },

  async getAdminJobSimulations(params) {
    return await JobSimulation.getJobSimulations(Object.assign(params, { fields: [] }));
  },

  async getPublicJobSimulations(params) {
    return await JobSimulation.getPublicJobSimulations(params);
  },

  async getAdminJobSimulation(jobSimulationId) {
    return await JobSimulation.getJobSimulation(jobSimulationId);
  },

  async updateLogo(jobSimulationId, req, file) {
    const uploadResult = await uploadFileToS3({ req, file, file_id: Date.now() });
    if (!uploadResult?.filepath) return null;

    const result = await JobSimulation.updateLogo(jobSimulationId, uploadResult.filepath);
    return result;
  },

  async updateCredentials(jobSimulationId, username, password) {
    return await JobSimulation.updateCredentials(jobSimulationId, username, password);
  },

  async getOrCreateProgress(data) {
    return await JobSimulationProgress.getOrCreate(data);
  },

  async getProgress(jobSimulationId, email) {
    const data = await JobSimulationProgress.getProgress({ jobSimulationId, email: email });
    return data;
  },

  async saveProgressConversationId(jobSimulationId, email, conversationId) {
    return await JobSimulationProgress.saveConversationId({ jobSimulationId, email, conversationId });
  },

  async saveProgressIntakeId(jobSimulationId, email, intakeId) {
    return await JobSimulationProgress.saveIntakeId({ jobSimulationId, email, intakeId });
  },

  async updateProgress(jobSimulationId, email, data) {
    return await JobSimulationProgress.update({ jobSimulationId, email, data });
  },

  async updateProgressEmailReplies(jobSimulationId, email, emailId, reply) {
    return await JobSimulationProgress.model.findOneAndUpdate(
      { jobSimulationId, email, 'emails.id': emailId },
      { $push: { 'emails.$.replies': reply } },
      { new: true },
    ).lean();
  },
};

module.exports = JobSimulationService;
