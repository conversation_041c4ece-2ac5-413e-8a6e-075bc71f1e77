import React from 'react';
import { Check, Users, Instagram } from 'lucide-react';

interface AudienceSelectorProps {
	selectedAudience: string;
	onSelect: (audience: string) => void;
	onClose: () => void;
}

const AudienceSelector: React.FC<AudienceSelectorProps> = ({
	selectedAudience,
	onSelect,
	onClose,
}) => {
	const audiences = [
		{
			id: 'groups-12',
			name: 'Groups for people over 12',
			icon: Users,
			checked: true,
		},
	];

	return (
		<div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
			<div className="p-3 border-b border-gray-200">
				<h3 className="text-sm font-medium text-gray-800">Post to Facebook and Instagram</h3>
			</div>

			<div className="p-3 space-y-3">
				{audiences.map((audience) => (
					<label key={audience.id} className="flex items-center space-x-3 cursor-pointer">
						<input
							type="checkbox"
							checked={audience.checked}
							onChange={() => onSelect(audience.name)}
							className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
						/>
						<div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
							<audience.icon className="w-4 h-4 text-white" />
						</div>
						<span className="text-sm text-gray-700">{audience.name}</span>
						{audience.checked && (
							<Check className="w-4 h-4 text-blue-500 ml-auto" />
						)}
					</label>
				))}

				<div className="border-t pt-3 mt-3">
					<label className="flex items-center space-x-3 cursor-pointer">
						<input
							type="checkbox"
							className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
						/>
						<div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
							<Instagram className="w-4 h-4 text-white" />
						</div>
						<span className="text-sm text-gray-700">Instagram Account</span>
						<button className="text-xs text-blue-500 hover:text-blue-700 ml-auto">
							Connect Instagram
						</button>
					</label>
					<p className="text-xs text-gray-500 mt-1 ml-11">
						Connect your Instagram and Facebook accounts to share information and improve your experience across both platforms.
					</p>
				</div>
			</div>
		</div>
	);
};

export default AudienceSelector;