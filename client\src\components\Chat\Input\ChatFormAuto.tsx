// import { memo, useEffect, useRef, useState } from 'react';
// import { useOutletContext } from 'react-router-dom';
// import { useChatContext } from '~/Providers';
// import { useAutoSave, useQueryParams, useSubmitMessage } from '~/hooks';
// import { TJobSimulationContext } from '~/common';

// // TODO: only run this component if conversationId === 'new'
// const ChatFormAuto = memo(
//   ({ index = 0, isTriggered = false }: { index?: number; isTriggered?: boolean }) => {
//     const textAreaRef = useRef<HTMLTextAreaElement>(null);

//     const { jobSimulationData, jobSimulationId } = useOutletContext<TJobSimulationContext>();

//     const [isChatTriggered] = useState<boolean>(isTriggered);

//     const { files, setFiles, conversation } = useChatContext();

//     useAutoSave({
//       conversationId: conversation?.conversationId,
//       textAreaRef,
//       files,
//       setFiles,
//     });

//     const { submitMessage } = useSubmitMessage();

//     useQueryParams({ textAreaRef });

//     useEffect(() => {
//       let timeoutId: NodeJS.Timeout | null = null;
//       // TODO: check jobSimulationData?.jobSimulationId
//       if (isChatTriggered && jobSimulationData?.jobSimulationId) {
//         timeoutId = setTimeout(() => {
//           submitMessage({
//             text: `I'm here for the Job Simulation`,
//             isTriggered,
//             jobSimulationId,
//           });
//         }, 6000);
//       }
//       return () => {
//         if (timeoutId) {
//           clearTimeout(timeoutId);
//         }
//       };
//     }, [isChatTriggered, jobSimulationData?.jobSimulationId]);

//     return <></>;
//   },
// );

// export default ChatFormAuto;
