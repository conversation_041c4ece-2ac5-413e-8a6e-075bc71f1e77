const { getLogStores } = require('~/cache');
const {
  CacheKeys
} = require('librechat-data-provider');

const JobSimulationInteractionService = {

  async getUserInteraction({ jobSimulationId, jobSimulationEmail, type }) {
    const cache = getLogStores(CacheKeys.JOB_SIMULATION);
    const cacheUserInteraction = await cache.get(`${CacheKeys.JOB_SIMULATION_INTERACTION_PREFIX}${jobSimulationId}_${jobSimulationEmail}`);
    return type ? { type: cacheUserInteraction[type] || {} } : cacheUserInteraction || {};
  },

  async deleteUserInteraction({ jobSimulationId, jobSimulationEmail }) {
    const cache = getLogStores(CacheKeys.JOB_SIMULATION);
    const key = `${CacheKeys.JOB_SIMULATION_INTERACTION_PREFIX}${jobSimulationId}_${jobSimulationEmail}`;
    const result = await cache.delete(key);
    return { success: result, key: key };
  },

  async saveUserInteraction({ jobSimulationId, jobSimulationEmail, interaction = {} }) {
    const cache = getLogStores(CacheKeys.JOB_SIMULATION);
    // TODO: We should use ID Session. In case we allow user to reset the progress
    const key = `${CacheKeys.JOB_SIMULATION_INTERACTION_PREFIX}${jobSimulationId}_${jobSimulationEmail}`;
    let cacheUserInteraction = (await cache.get(key) || {});

    const interactionType = interaction.type;
    if (interactionType === 'login') {
      cacheUserInteraction['isLogin'] = true;
    } else if (interactionType === 'logout') {
      cacheUserInteraction = { isLogin: false };
    } else if (interactionType === 'open-app') {
      const appId = interaction.appData?.appId || 'home';
      cacheUserInteraction['app'] = {
        appId: appId,
      };
      if (appId === 'meeting') {
        if (interaction.appData?.meetings) {
          cacheUserInteraction['meetings'] = interaction.appData?.meetings || [];
        } else if (interaction.appData?.meeting) {
          let meetings = cacheUserInteraction['meetings'] || [];
          if (meetings.length === 0) {
            meetings.push(interaction.appData.meeting);
          } else {
            const meetingId = interaction.appData?.meeting?.id;
            meetings = meetings.map(meeting => meeting.id === meetingId ? interaction.appData.meeting : meeting);
          }
          cacheUserInteraction['meetings'] = meetings;
          cacheUserInteraction['currentMeeting'] = interaction.appData.meeting;
        }
      } else if (appId === 'task-board') {
        if (interaction.appData?.tasks) {
          cacheUserInteraction['tasks'] = interaction.appData?.tasks || [];
        } else if (interaction.appData?.task) {
          let tasks = cacheUserInteraction['tasks'] || [];
          if (tasks.length === 0) {
            tasks.push(interaction.appData.task);
          } else {
            const taskId = interaction.appData?.task?.taskId;
            tasks = tasks.map(task => task.taskId === taskId ? interaction.appData.task : task);
          }
          cacheUserInteraction['tasks'] = tasks;
          cacheUserInteraction['currentTask'] = interaction.appData.task;
        }
      }
    } else if (interactionType === 'close-app') {
      delete cacheUserInteraction['app'];
    } else if (interactionType === 'complete-task') {
      const taskId = interaction.appData?.task?.taskId;
      if (taskId) {
        let tasks = cacheUserInteraction['tasks'] || [];
        tasks = tasks.map(task => {
          if (task.taskId === taskId) {
            task.status = 'passed';
          }
          return task;
        });
        cacheUserInteraction['tasks'] = tasks;
        if (cacheUserInteraction['currentTask'] && cacheUserInteraction['currentTask']?.taskId === taskId) {
          cacheUserInteraction['currentTask'].status = 'passed';
        }
      }

    } else if (interactionType === 'complete-meeting') {
      const completedMeetingId = interaction.appData?.meetingId;
      if (completedMeetingId) {
        let meetings = cacheUserInteraction['meetings'] || [];
        meetings = meetings.map(meeting => {
          if (meeting.id === completedMeetingId) {
            meeting.status = 'completed';
          }
          return meeting;
        });
        cacheUserInteraction['meetings'] = meetings;
        if (cacheUserInteraction['currentMeeting'] && cacheUserInteraction['currentMeeting']?.id === completedMeetingId) {
          cacheUserInteraction['currentMeeting'].status = 'completed';
        }
      }
    } else if (interactionType === 'update-tasks') {
      const tasks = interaction?.tasks || [];
      cacheUserInteraction['tasks'] = tasks.map((t) => {
        return {
          taskId: t.taskId,
          taskName: t.taskName,
          taskDescription: t.taskDescription,
          status: t.status,
        }
      });
    } else if (interactionType === 'update-task') {
      if (interaction?.task?.taskId) {
        let tasks = cacheUserInteraction['tasks'] || [];
        tasks = tasks.map(task => {
          if (task.taskId === interaction?.task?.taskId) {
            task.status = interaction?.task?.status;
          }
          return task;
        });
        cacheUserInteraction['tasks'] = tasks;
      }
    }

    if (!interaction.appData?.task)
      delete cacheUserInteraction['currentTask'];

    if (!interaction.appData?.meeting)
      delete cacheUserInteraction['currentMeeting'];

    cache.set(key, cacheUserInteraction);


    return { success: true, interaction: interaction };
  },
};

module.exports = JobSimulationInteractionService;
