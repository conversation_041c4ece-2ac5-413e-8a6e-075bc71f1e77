import { getAppData } from '~/components/JobSimulation/AppSimulation/CalculationDataStorage';
import AppSimulation from './AppSimulation';

interface AppSimulationWrapperProps {
  appSimulationId: string;
}

const AppSimulationWrapper = ({ appSimulationId }: AppSimulationWrapperProps) => {
  const appData = getAppData(appSimulationId);
  if (!appData?.appSimulationConfig || !appData?.appSimulationScreens) {
    return <></>;
  }
  return (
    <AppSimulation
      appSimulationScreens={appData.appSimulationScreens}
      appSimulationConfig={appData.appSimulationConfig}
    />
  );
};

export default AppSimulationWrapper;
