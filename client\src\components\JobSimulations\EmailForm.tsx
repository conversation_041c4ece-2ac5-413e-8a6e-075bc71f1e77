import MarkdownIt from 'markdown-it';
import MdEditor from 'react-markdown-editor-lite';
import 'react-markdown-editor-lite/lib/index.css';
import { Controller } from 'react-hook-form';

export default function EmailForm({ form }) {
	const { register } = form;
	const mdParser = new MarkdownIt();

	return (
		<div className="space-y-4">
			<div>
				<label>Title</label>
				<input {...register('title')} className="w-full border px-2 py-1" />
			</div>

			<div>
				<label>Sender Name</label>
				<input {...register('sender.name')} className="w-full border px-2 py-1" />
			</div>

			<div>
				<label>Sender Role</label>
				<input {...register('sender.role')} className="w-full border px-2 py-1" />
			</div>
			<div>
				<label>Sender Avatar (Upload)</label>
				<input
					type="file"
					accept="image/*"
					className="w-full border px-2 py-1"
					onChange={(e) => {
						const file = e.target.files?.[0];
						if (file) {
							const url = URL.createObjectURL(file);
							form.setValue('sender.avatar', url);
						}
					}}
				/>
			</div>

			<div>
				<label>Sender Email</label>
				<input {...register('sender.email')} className="w-full border px-2 py-1" />
			</div>

			<div>
				<label>Greeting</label>
				<input {...register('greeting')} className="w-full border px-2 py-1" />
			</div>

			<div>
				<label>Content</label>
				<Controller
					name="content"
					control={form.control}
					render={({ field }) => (
						<MdEditor
							value={field.value}
							style={{ height: '300px' }}
							renderHTML={(text) => mdParser.render(text)}
							onChange={({ text }) => field.onChange(text)}
							config={{ view: { menu: true, md: true, html: false } }}
						/>
					)}
				/>
			</div>

			<div>
				<label>Signature</label>
				<textarea {...register('signature')} rows={3} className="w-full border px-2 py-1" />
			</div>

			<div>
				<label>Type</label>
				<select {...register('type')} className="w-full border px-2 py-1">
					<option value="Meeting">Meeting</option>
					<option value="Reference Letter">Reference Letter</option>
				</select>
			</div>

			<div>
				<label>Trigger Action</label>
				<select {...register('trigger.action')} className="w-full border px-2 py-1">
					<option value="">None</option>
					<option value="nextEmail">Next Email</option>
					<option value="triggerAssistant">Trigger Assistant</option>
				</select>
			</div>

			{/* Conditional inputs */}
			{form.watch('trigger.action') === 'nextEmail' && (
				<div>
					<label>Next Email ID</label>
					<input {...register('trigger.nextEmailId')} className="w-full border px-2 py-1" />
				</div>
			)}
			{form.watch('trigger.action') === 'triggerAssistant' && (
				<div>
					<label>Assistant Prompt</label>
					<input {...register('trigger.assistantPrompt')} className="w-full border px-2 py-1" />
				</div>
			)}
		</div>
	);
}