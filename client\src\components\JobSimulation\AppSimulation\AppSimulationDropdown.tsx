import { AppSimulationElementDropdownOption } from '~/common';
import {
  getDropdownStyle
} from '~/components/JobSimulation/AppSimulation/AppSimulationHelpers';

interface AppSimulationDropdownProps {
  showDropdown: boolean;
  dropdownOptions: AppSimulationElementDropdownOption[];
  handleClickOption: (option: AppSimulationElementDropdownOption) => void;
  dropdownPosition: { x: number; y: number };
}

const AppSimulationDropdown = ({
  showDropdown,
  dropdownOptions,
  handleClickOption,
  dropdownPosition,
}: AppSimulationDropdownProps) => {
  if (!showDropdown) return null;

  return (
    <>
      <div style={getDropdownStyle(dropdownPosition)} onClick={(e) => e.stopPropagation()}>
        {dropdownOptions.map((option, index) => (
          <div
            key={index}
            style={{
              padding: '8px 12px',
              cursor: 'pointer',
              borderBottom: index < dropdownOptions.length - 1 ? '1px solid #eee' : 'none',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'white';
            }}
            onClick={() => {
              handleClickOption(option);
            }}
          >
            {option.label}
          </div>
        ))}
      </div>
    </>
  );
};

export default AppSimulationDropdown;
