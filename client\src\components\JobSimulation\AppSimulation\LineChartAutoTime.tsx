import { useEffect, useState } from 'react';
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  <PERSON>ltip,
  <PERSON>Axis,
  YAxis,
} from 'recharts';

interface LineChartAutoTimeProps {
  data: {
    labelX: string;
    labelY: string;
    lines: any[];
    datasets: any[];
  };
  chartArea: {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
  };
}

const LineChartAutoTime = (props: LineChartAutoTimeProps) => {
  const {
    chartArea,
    data: { datasets, labelX, labelY, lines },
  }: LineChartAutoTimeProps = props;
  // Start timer when component mounts
  const [elapsedTime, setElapsedTime] = useState(0);
  const [chartData, setChartData] = useState<any[]>();
  const [startTime, _setStartTime] = useState<number>(Date.now());

  useEffect(() => {
    setChartData(lines.map((line: any) => ({ [labelX]: line.name, [line.name]: 0 })));
  }, []);

  useEffect(() => {
    if (startTime === null) return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      setElapsedTime(elapsed);
    }, 1000); // Update every second for more accurate timing

    return () => clearInterval(interval);
  }, [startTime]);

  useEffect(() => {
    const visiblePoints = datasets.filter((point) => point.time <= elapsedTime);
    const convertedChartData = visiblePoints.map((point) => ({
      [labelX]: point.x,
      ...point.dataLines,
    }));
    if (convertedChartData.length > 0) {
      setChartData(convertedChartData);
    }
  }, [elapsedTime]);

  return (
    <div
      className="relative"
      style={{
        position: 'absolute',
        left: `${chartArea.x1}%`,
        top: `${chartArea.y1}%`,
        zIndex: 15,
        pointerEvents: 'auto',
        backgroundColor: '#fff',
        width: `${chartArea.x2 - chartArea.x1}%`,
        height: `${chartArea.y2 - chartArea.y1}%`,
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          style={{
            width: `100%`,
            maxHeight: `100%`,
          }}
          data={chartData}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis fontSize={13} dataKey={labelX} padding={{ left: 5, right: 5 }} />
          <YAxis fontSize={13} />
          <Tooltip labelStyle={{ fontSize: '1rem' }} itemStyle={{ fontSize: '1rem' }} />
          {lines.map((line: any) => (
            <Line
              key={line.name}
              type={line.type || 'monotone'}
              dataKey={line.name || labelY}
              stroke={line.stroke || '#8884d8'}
              activeDot={{ r: 8 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      {/* <div
        style={{
          position: 'absolute',
          top: '-30px',
          left: '0',
          fontSize: '10px',
          color: '#666',
          background: 'rgba(255,255,255,0.8)',
          padding: '2px 4px',
          borderRadius: '2px',
        }}
      >
        Time: {elapsedTime}s | Points: {visiblePoints}/{datasets.length}
        Time: {elapsedTime}s
      </div> */}
    </div>
  );
};

export default LineChartAutoTime;
