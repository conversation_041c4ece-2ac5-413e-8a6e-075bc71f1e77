const feedbackMessages = {
    passed: [
        "Great job! You nailed it.",
        "Well done — everything looks correct!",
        "Solid work! Your task was spot on.",
        "Excellent submission!",
        "Nicely done! You got it right.",
        "Everything checks out perfectly — great work!",
        "You did a fantastic job on this one!",
        "Impressive work! Keep it up.",
        "Your answer is correct — well done!",
        "That was a smooth submission! Great work."
    ],
    failed: [
        "Thanks for submitting! There's a small issue.",
        "Good try — but not quite there yet.",
        "Almost got it! Let's take another look.",
        "Appreciate the effort! Here's what needs adjusting.",
        "You're getting close — just a few more steps.",
        "Not the correct answer this time, but you're on the right path.",
        "Thanks for your submission! Some parts need revision.",
        "You're making progress — keep going.",
        "A good attempt! Just needs a few tweaks.",
        "Keep going! Mistakes help us learn."
    ],
    default: [
        "Thanks for your submission.",
        "Appreciate your effort on this task.",
        "Submission received — thank you!",
        "Got your submission! We'll review it shortly.",
        "Thanks! We'll take a look and get back to you."
    ]
};

const getRandomFeedback = (status, feedback = "") => {
    let messages;

    if (status === 'passed' || status === 'failed') {
        messages = feedbackMessages[status];
    } else {
        messages = feedbackMessages.default;
    }

    const randomIndex = Math.floor(Math.random() * messages.length);
    const baseMessage = messages[randomIndex];

    if (feedback.trim()) {
        return `${baseMessage} Feedback:\n${feedback}`;
    } else {
        return baseMessage;
    }
}

module.exports = {
    getRandomFeedback
};