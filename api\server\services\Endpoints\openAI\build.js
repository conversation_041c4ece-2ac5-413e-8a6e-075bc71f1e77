const { removeNullishValues } = require('librechat-data-provider');
// const generateArtifactsPrompt = require('~/app/clients/prompts/artifacts');
const generateCustomArtifactsPrompt = require('~/app/clients/prompts/customArtifactPrompt');

const buildOptions = (endpoint, parsedBody) => {
  const {
    modelLabel,
    chatGptLabel,
    promptPrefix,
    maxContextTokens,
    resendFiles = true,
    imageDetail,
    iconURL,
    greeting,
    spec,
    artifacts,
    ...modelOptions
  } = parsedBody;

  const endpointOption = removeNullishValues({
    endpoint,
    modelLabel,
    chatGptLabel,
    promptPrefix,
    resendFiles,
    imageDetail,
    iconURL,
    greeting,
    spec,
    maxContextTokens,
    modelOptions,
  });

  // TODO: quick fix for custom artifacts prompt. Must use domain custom.
  // Here: api\server\services\Endpoints\custom\build.js

  // if (typeof artifacts === 'string') {
  //   endpointOption.artifactsPrompt = generateArtifactsPrompt({ endpoint, artifacts });
  // }

  endpointOption.artifactsPrompt = generateCustomArtifactsPrompt();

  return endpointOption;
};

module.exports = buildOptions;
