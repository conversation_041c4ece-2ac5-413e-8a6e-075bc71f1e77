export type RedirectButtonProps = {
  title: string;
  link: string;
};

const RedirectButton = ({ title, link }: RedirectButtonProps) => {
  const handleClick = () => {
    if (!link) return;
    console.log('No link provided');
    if (link.includes('http://') || link.includes('https://')) {
      window.location.href = link;
    } else window.location.replace(link);
  };

  return (
    <div className="group relative my-4 rounded-xl text-sm text-text-primary">
      <button
        type="button"
        title={title || ''}
        onClick={handleClick}
        className="relative overflow-hidden rounded-xl border border-border-medium transition-all duration-300 hover:border-border-xheavy hover:shadow-lg"
      >
        <div className="w-fit bg-surface-tertiary p-2">
          <div className="flex flex-row items-center gap-2">
            <div className="overflow-hidden text-left">
              <div className="truncate font-medium">{title || ''}</div>
              <div className="truncate text-text-secondary">Click to continue</div>
            </div>
          </div>
        </div>
      </button>
      <br />
    </div>
  );
};

export default RedirectButton;
