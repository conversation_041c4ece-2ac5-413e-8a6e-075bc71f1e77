import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import EmailForm from './EmailForm';
import EmailReview from './EmailReview';

export default function EmailDetailPage() {
	const form = useForm({
		defaultValues: {
			title: '',
			sender: {
				name: '',
				role: '',
				avatar: '',
				email: '',
			},
			greeting: 'Hi {username}',
			content: '',
			signature: 'Warm regards,\n{sender_name} - {sender_role}\n{company_name}',
			type: 'Meeting',
			trigger: {
				action: '',
				nextEmailId: '',
				assistantPrompt: '',
			},
		},
	});

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
			<EmailForm form={form} />
			<EmailReview values={form.watch()} />
		</div>
	);
}