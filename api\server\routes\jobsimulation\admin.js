const multer = require('multer');
const express = require('express');
const router = express.Router();
const JobSimulationController = require('~/server/controllers/JobSimulationController');
const { requireJwtAuth } = require('~/server/middleware');
const { storage } = require('~/server/routes/files/multer');

const uploadJobSimulation = multer({ storage });

router.use(requireJwtAuth);

// TODO: add middleware check admin
router.get('/list', JobSimulationController.getAdminJobSimulations);
router.get('/:jobSimulationId', JobSimulationController.getAdminJobSimulation);
router.patch('/:jobSimulationId/logo', uploadJobSimulation.single('file'), JobSimulationController.updateLogo);
router.patch('/:jobSimulationId/credentials', JobSimulationController.updateCredentials);

module.exports = router;
