const BitMeetService = require('../services/BitMeet/BitMeetService');

const BitMeetController = {
  async getOrCreateBitMeetUser(req, res) {
    try {
      const data = await BitMeetService.getOrCreateBitMeetUser({
        username: req.body.email,
        email: req.body.email,
        avatar: req.user.avatar,
        name: req.user.name,
      });
      res.json(data);
    } catch (error) {
      console.error(error);
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get or create BitMeet user' });
    }
  },
};

module.exports = BitMeetController;
