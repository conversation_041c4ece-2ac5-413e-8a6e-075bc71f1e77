import React, { useRef } from 'react';
import { Camera, Video, X, Edit, Tag, Trash2, ChevronDown, Edit2 } from 'lucide-react';
import { Button } from '~/components/ui';

interface MediaUploadProps {
	selectedImages: File[];
	onImageUpload: (files: File[]) => void;
	onRemoveImage: (index: number) => void;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
	selectedImages,
	onImageUpload,
	onRemoveImage,
}) => {
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
		const files = Array.from(event.target.files || []);
		if (files.length > 0) {
			onImageUpload(files);
		}
	};

	const handleAddPhotos = () => {
		fileInputRef.current?.click();
	};

	return (
		<div className='bg-white drop-shadow-xl rounded p-4'>
			<div className='mb-4'>
				<p className="text-base font-medium">
					Media
				</p>
				<p className="text-sm">Share photos or videos.</p>
			</div>

			{selectedImages.length === 0 ? (
				<div className="space-y-2">
					<button
						onClick={handleAddPhotos}
						className="w-fit flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<Camera className="w-4 h-4 text-gray-500" />
						<span className="text-sm text-gray-700">Add Photo</span>
					</button>
				</div>
			) : (
				<div className="border border-gray-300 rounded-lg p-4">
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center space-x-2">
							<span className="text-sm font-medium">Media</span>
							<span className="text-xs text-gray-500">Share photos or videos.</span>
						</div>
					</div>

					<div className="space-y-4">
						{selectedImages.map((image, index) => (
							<div key={index} className="flex items-center justify-between">
								<div className='flex items-center gap-4'>
									<img
										src={URL.createObjectURL(image)}
										alt={`Upload ${index + 1}`}
										className="w-14 h-14 object-contain"
									/>
									<div className="text-sm">
										1080 x 1080
									</div>
								</div>
								<div className="flex items-center justify-end">
									<div className="flex space-x-2">
										<Button variant="outline" className="w-10 h-10 p-2">
											<Edit2 className="w-full h-full" />
										</Button>
										<Button variant="outline" className="w-10 h-10 p-2">
											<Tag className="w-full h-full" />
										</Button>
										<Button variant="outline" className="w-10 h-10 p-2" onClick={() => onRemoveImage(index)}>
											<Trash2 className="w-full h-full" />
										</Button>
									</div>
								</div>
							</div>
						))}
					</div>

					<button
						onClick={handleAddPhotos}
						className="w-fit mt-3 flex items-center justify-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<Camera className="w-4 h-4 text-gray-500" />
						<span className="text-sm text-gray-700">Add Photo</span>
					</button>
				</div>
			)}

			<input
				ref={fileInputRef}
				type="file"
				accept="image/*"
				multiple
				onChange={handleFileSelect}
				className="hidden"
			/>
		</div>
	);
};

export default MediaUpload;