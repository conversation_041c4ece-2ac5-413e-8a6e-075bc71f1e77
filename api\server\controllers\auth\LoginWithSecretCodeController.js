const { setAuthTokens } = require('~/server/services/AuthService');
const { Key, findUser } = require('~/models');
const { logger } = require('~/config');

const loginWithSecretCodeController = async (req, res) => {
  try {
    const { secretCode } = req.body;

    if (!secretCode) {
      return res.status(400).json({ message: 'Secret code is required' });
    }

    const key = await Key.findOne({ key: secretCode });
    logger.info({ key }, 'key')
    if (!key) {
      return res.status(401).json({ message: 'Invalid secret code' });
    }

    const user = await findUser({ _id: key.userId });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    const { password: _p, totpSecret: _t, __v, secretCode: _s, ...safeUser } = user;
    safeUser.id = user._id.toString();

    const token = await setAuthTokens(user._id, res);
    await Key.deleteOne({ _id: key._id });

    return res.status(200).send({ token, user: safeUser });
  } catch (err) {
    logger.error('[loginWithSecretCodeController]', err);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};


module.exports = {
  loginWithSecretCodeController,
};
