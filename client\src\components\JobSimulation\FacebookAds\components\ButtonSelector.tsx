import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/Select';
import { Label, RadioGroup, RadioGroupItem } from '~/components/ui';


const ButtonSelector = () => {
	const [selectedButton, setSelectedButton] = useState('send-message');
	const [buttonLabel, setButtonLabel] = useState('Send message');

	const buttonOptions = [
		{ value: 'no-button', label: 'No button' },
		{ value: 'book-now', label: 'Book now' },
		{ value: 'learn-more', label: 'Learn more' },
		{ value: 'shop-now', label: 'Shop now' },
		{ value: 'sign-up', label: 'Sign up' },
		{ value: 'send-message', label: 'Send message' },
		{ value: 'send-whatsapp', label: 'Send WhatsApp message' },
		{ value: 'call-now', label: 'Call now' },
	];

	const labelOptions = [
		'No button',
		'Book now',
		'Learn more',
		'Shop now',
		'Sign up',
		'Send message',
		'Send WhatsApp message',
		'Call now',
	];

	return (
		<div className="bg-white rounded-lg p-4 drop-shadow-lg">
			{/* Header */}
			<div className="flex items-center justify-between mb-4">
				<p className="text-lg font-medium">Button</p>
				<div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
					<span className="text-white text-sm">i</span>
				</div>
			</div>

			{/* Button Label Section */}
			<div>
				<p className="text-base font-medium mb-3">
					Button label
				</p>
				<Select value={buttonLabel} onValueChange={setButtonLabel}>
					<SelectTrigger className="w-full h-12 text-base border bg-white">
						<SelectValue />
					</SelectTrigger>
					<SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
						{labelOptions.map((option) => (
							<SelectItem key={option} value={option} className="text-base">
								{option}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Radio Button Options */}
			{/* <RadioGroup value={selectedButton} onValueChange={setSelectedButton} className="space-y-4">
				{buttonOptions.map((option) => (
					<div
						key={option.value}
						className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${selectedButton === option.value
								? 'bg-blue-50 border border-blue-200'
								: 'hover:bg-gray-50'
							}`}
					>
						<RadioGroupItem
							value={option.value}
							id={option.value}
							className="w-5 h-5 border-2 border-gray-300 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"
						/>
						<Label
							htmlFor={option.value}
							className="text-base text-gray-700 cursor-pointer flex-1"
						>
							{option.label}
						</Label>
					</div>
				))}
			</RadioGroup> */}
		</div>
	);
};

export default ButtonSelector;