import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Info } from 'lucide-react';
import { Avatar, Button } from '~/components/ui';
import TargetingSection from './components/TargetingSection';
import BudgetScheduleSection from './components/BudgetScheduleSection';
import AdPreviewSection from './components/AdPreviewSection';
import ButtonSelector from './components/ButtonSelector';

const AdDetail = ({ onBackToAdList }) => {
	const { id } = useParams();
	const navigate = useNavigate();

	return (
		<div className="space-y-4">
			<div>
				<div
					className="flex items-center space-x-1 cursor-pointer"
					onClick={onBackToAdList}
				>
					<ArrowLeft className="w-6 h-6" />
					<span className='text-lg font-medium'>Boost post</span>
				</div>
			</div>
			<div>
				<div className="grid grid-cols-12 gap-6">
					{/* Left Column - Main Content */}
					<div className="col-span-8 space-y-6">
						<div className='bg-white drop-shadow-md p-4 rounded-lg'>
							<p className='text-lg font-medium'>Goal</p>
							<p className='text-sm font-normal'>What results would you like from this ad?</p>
							<div className='flex items-center justify-between mt-3'>
								<div className='flex flex-start gap-1.5'>
									<Avatar className='w-10 h-10 bg-black' />
									<div>
										<p className='text-base font-medium flex items-center gap-1'>Get more messages <Info className="w-4 h-4" /></p>
										<p className='text-xs text-[#929294]'>Show your ad to people who are likely to send you a message on Facebook, WhatsApp, or Instagram.</p>
									</div>
								</div>
								<Button variant="outline" className='rounded font-normal'>Change</Button>
							</div>
						</div>
						<ButtonSelector />
						<TargetingSection />
						<BudgetScheduleSection />
					</div>

					{/* Right Column - Preview */}
					<div className="col-span-4">
						<AdPreviewSection />
					</div>
				</div>
			</div>
			<div className="py-6 bg-white drop-shadow-xl flex items-center justify-center gap-10">
				<p className='text-xs font-normal'>By clicking Publish, you agree to Meta's <span className='text-blue-400 cursor-pointer'>Terms & conditions</span>, <span className='text-blue-400 cursor-pointer'>Help Center</span></p>
				<div className='text-xs font-normal text-[#929294]'>
					<p>This ad will save as a draft automatically if you exit before</p>
					<p className='text-end'>submitting.</p>
				</div>
				<div className="flex items-center space-x-2">
					<Button variant="outline" className='rounded'>Cancel</Button>
					<Button className='bg-blue-500 hover:bg-blue-600 rounded'>Publish</Button>
				</div>
			</div>
		</div >
	);
};

export default AdDetail;