import { useEffect, useState } from 'react';

interface LineChartProps {
  data: {
    datasets: Array<{ x: number; y: number; label: string; time: number }>;
    chartArea: {
      left: number;
      top: number;
      width: number;
      height: number;
    };
  };
}

const LineChart = ({ data }: LineChartProps) => {
  const { datasets, chartArea } = data;
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [visiblePoints, setVisiblePoints] = useState<number>(0);
  const [startTime, setStartTime] = useState<number | null>(null);

  // Start timer when component mounts
  useEffect(() => {
    setStartTime(Date.now());
    setElapsedTime(0);
  }, []);

  // Timer for real-time updates
  useEffect(() => {
    if (startTime === null) return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      setElapsedTime(elapsed);
    }, 1000); // Update every second for more accurate timing

    return () => clearInterval(interval);
  }, [startTime]);

  // Update visible points based on elapsed time
  useEffect(() => {
    const newVisiblePoints = datasets.filter((point) => point.time <= elapsedTime).length;
    setVisiblePoints(newVisiblePoints);
  }, [elapsedTime, datasets]);

  // Get currently visible datasets
  const currentDatasets = datasets.slice(0, visiblePoints);

  // Create SVG path from visible datasets
  const createPath = () => {
    if (currentDatasets.length === 0) return '';

    // Convert absolute percentage coordinates to relative coordinates within the chart area
    const points = currentDatasets.map((point) => ({
      x: ((point.x - chartArea.left) / chartArea.width) * 100,
      y: ((point.y - chartArea.top) / chartArea.height) * 100,
    }));

    let path = `M ${points[0].x} ${points[0].y}`;
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`;
    }

    return path;
  };

  return (
    <div
      style={{
        position: 'absolute',
        left: `${chartArea.left}%`,
        top: `${chartArea.top}%`,
        width: `${chartArea.width}%`,
        height: `${chartArea.height}%`,
        zIndex: 15,
        pointerEvents: 'auto',
      }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{
          overflow: 'visible',
        }}
      >
        {/* Chart line */}
        {currentDatasets.length > 1 && (
          <path
            d={createPath()}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
            style={{
              filter:
                hoveredPoint !== null ? 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))' : 'none',
              transition: 'filter 0.2s ease',
            }}
          />
        )}

        {/* Data points */}
        {currentDatasets.map((point, index) => {
          const x = ((point.x - chartArea.left) / chartArea.width) * 100;
          const y = ((point.y - chartArea.top) / chartArea.height) * 100;

          return (
            <g key={index}>
              <circle
                cx={x}
                cy={y}
                r={hoveredPoint === index ? '1' : '0.8'}
                fill="#3b82f6"
                stroke="white"
                strokeWidth="2"
                vectorEffect="non-scaling-stroke"
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  filter:
                    hoveredPoint === index
                      ? 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.8))'
                      : 'none',
                  opacity: index === currentDatasets.length - 1 ? 1 : 0.8, // Highlight newest point
                }}
                onMouseEnter={() => setHoveredPoint(index)}
                onMouseLeave={() => setHoveredPoint(null)}
              />

              {/* Tooltip on hover */}
              {hoveredPoint === index && (
                <g>
                  {/* <rect
                    x={x + 1}
                    y={y - 10}
                    width="10"
                    height="10"
                    fill="rgba(0, 0, 0, 0.8)"
                    vectorEffect="non-scaling-stroke"
                  /> */}
                  <text
                    x={x + 2}
                    y={y - 2}
                    fill="rgba(0, 0, 0, 0.8)"
                    fontSize="3"
                    textAnchor="middle"
                    vectorEffect="non-scaling-stroke"
                  >
                    {point.label}
                  </text>
                </g>
              )}
            </g>
          );
        })}
      </svg>

      <div
        style={{
          position: 'absolute',
          top: '-20px',
          left: '0',
          fontSize: '10px',
          color: '#666',
          background: 'rgba(255,255,255,0.8)',
          padding: '2px 4px',
          borderRadius: '2px',
        }}
      >
        {/* Time: {elapsedTime}s | Points: {visiblePoints}/{datasets.length} */}
        Time: {elapsedTime}s
      </div>
    </div>
  );
};

export default LineChart;
