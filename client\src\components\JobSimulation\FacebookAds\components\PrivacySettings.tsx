import React, { useState } from 'react';
import { RadioGroup, RadioGroupItem } from '~/components/ui';

interface PrivacySettingsProps {
	onCancel?: () => void;
	onSchedule?: (settings: { privacy: string; boost: boolean }) => void;
}

const PrivacySettings = ({ onCancel, onSchedule }: PrivacySettingsProps) => {
	const [selectedPrivacy, setSelectedPrivacy] = useState('public');

	return (
		<div className="bg-white rounded drop-shadow-xl p-4">
			{/* Privacy Settings Section */}
			<div>
				<p className="text-base font-medium">Privacy settings</p>
				<p className="text-sm mb-4">
					Adjust your privacy settings to control who can see your post in News Feed, Watch,
					search results and on your profile.
				</p>

				<RadioGroup
					value={selectedPrivacy}
					onValueChange={setSelectedPrivacy}
				>
					<label className="flex items-start space-x-4 cursor-pointer p-4 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 transition-colors">
						<RadioGroupItem
							value="public"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-base font-normal">
								Public
							</div>
							<div className="text-xs">
								Anyone on or off Facebook will be able to see your post.
							</div>
						</div>
					</label>

					<label className="flex items-start space-x-4 cursor-pointer p-4 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 transition-colors">
						<RadioGroupItem
							value="restricted"
							className="mt-1 flex-shrink-0"
						/>
						<div className="flex-1">
							<div className="text-base font-normal">
								Restricted
							</div>
							<div className="text-xs">
								Choose certain people on Facebook who can see your post.
							</div>
						</div>
					</label>
				</RadioGroup>
			</div>
		</div>
	);
};

export default PrivacySettings;