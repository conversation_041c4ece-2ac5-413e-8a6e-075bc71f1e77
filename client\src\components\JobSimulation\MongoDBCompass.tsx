import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import { Label, Input } from '~/components';
import {
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
} from '~/components';
import { cn } from '~/utils';
import store from '~/store';

// Define the input field interface
interface InputField {
  id: string;
  label: string;
  expectedValue: string;
  // Optional coordinates for clickable input fields (for mode 2)
  x1?: number;
  y1?: number;
  x2?: number;
  y2?: number;
  // Flag to indicate if this input is for mode 2 (clickable input)
  clickable?: boolean;
}

// Extended screen interface with inputs
interface Screen {
  id: string;
  title: string;
  image: string;
  bgColor: string;
  triggerMessage?: string;
  buttons?: Array<{
    title: string;
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    left: number;
    top: number;
    width: number;
    height: number;
    action: {
      type: string;
    };
  }>;
  inputs?: InputField[];
  actions?: Array<{
    type: string;
  }>;
}

const screens: Screen[] = [
  {
    id: '001',
    title: 'Modal start',
    image: '/assets/job-simulation/mongodb-compass/001.png',
    bgColor: 'bg-[#64767e]',
    triggerMessage: 'I want to start the MongoDB Compass Simulation',
    buttons: [
      {
        title: 'Start',
        x1: 33.12,
        y1: 68.6,
        x2: 67.12,
        y2: 75.32,
        left: 33.12,
        top: 68.6,
        width: 67.12 - 33.12,
        height: 75.32 - 68.6,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '002',
    title: 'Welcome with Add new connection buttons',
    image: '/assets/job-simulation/mongodb-compass/002.png',
    bgColor: 'bg-[#64767e]',
    triggerMessage: 'I want to add a new connection',
    buttons: [
      {
        title: 'Add new connection',
        x1: 0.94,
        y1: 30.49,
        x2: 18.58,
        y2: 35.89,
        left: 0.94,
        top: 30.49,
        width: 18.58 - 0.94,
        height: 35.89 - 30.49,

        action: {
          type: 'nextScreen',
        },
      },
      {
        title: 'Add new connection',
        x1: 55.09,
        y1: 43.07,
        x2: 66.28,
        y2: 48.17,
        left: 55.09,
        top: 43.07,
        width: 66.28 - 55.09,
        height: 48.17 - 43.07,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '003',
    title: 'Modal connect',
    image: '/assets/job-simulation/mongodb-compass/004.png',
    triggerMessage: 'What is the connection string?',
    bgColor: 'bg-[#64767e]',
    // buttons: [
    //   {
    //     title: 'Save & Connect',
    //     x1: 84.63,
    //     y1: 88.9,
    //     x2: 97.5,
    //     y2: 96.38,
    //     left: 84.63,
    //     top: 88.9,
    //     width: 97.5 - 84.63,
    //     height: 96.38 - 88.9,

    //     action: {
    //       type: 'nextScreen',
    //     },
    //   },
    // ],
    // Mode 1: Inputs shown when clicking anywhere on the screen
    inputs: [
      {
        id: 'connection_string',
        label: 'Connection String',
        expectedValue: 'mongodb://localhost:27017',
      },
      {
        id: 'connection_name',
        label: 'Connection Name',
        expectedValue: 'Local Host',
      },
    ],
  },
  {
    id: '004',
    title: 'New Database',
    image: '/assets/job-simulation/mongodb-compass/006.png',
    bgColor: 'bg-[#64767e]',
    triggerMessage: 'I want to create a new database',
    buttons: [
      {
        title: 'New Database',
        x1: 13.89,
        y1: 25.3,
        x2: 15.57,
        y2: 28.05,
        left: 13.89,
        top: 25.3,
        width: 15.57 - 13.89,
        height: 28.05 - 25.3,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '005',
    title: 'Modal create database',
    image: '/assets/job-simulation/mongodb-compass/007.png',
    bgColor: 'bg-[#64767e]',
    // buttons: [
    //   {
    //     title: 'Create Database',
    //     x1: 72.04,
    //     y1: 84.73,
    //     x2: 94.54,
    //     y2: 92.79,
    //     left: 72.04,
    //     top: 84.73,
    //     width: 94.54 - 72.04,
    //     height: 92.79 - 84.73,

    //     action: {
    //       type: 'nextScreen',
    //     },
    //   },
    // ],
    // Mode 1: Inputs shown when clicking anywhere on the screen
    inputs: [
      {
        id: 'database_name',
        label: 'Database Name',
        expectedValue: 'LocalDatabase',
      },
      {
        id: 'collection_name',
        label: 'Collection Name',
        expectedValue: 'Posts',
      },
    ],
  },
  {
    id: '006',
    title: 'Import Data',
    image: '/assets/job-simulation/mongodb-compass/008.png',
    bgColor: 'bg-[#64767e]',
    triggerMessage: 'I want to import data',
    buttons: [
      {
        title: 'Import Data',
        x1: 56.25,
        y1: 67.23,
        x2: 63.2,
        y2: 70.7,
        left: 56.25,
        top: 67.23,
        width: 63.2 - 56.25,
        height: 70.7 - 67.23,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '007',
    title: 'Dialog Import Data',
    image: '/assets/job-simulation/mongodb-compass/009.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Import',
        x1: 62.97,
        y1: 62.02,
        x2: 67.21,
        y2: 66.59,
        left: 62.97,
        top: 62.02,
        width: 67.21 - 62.97,
        height: 66.59 - 62.02,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '008',
    title: 'Import Data Completed',
    image: '/assets/job-simulation/mongodb-compass/010.png',
    triggerMessage: 'I want to query data',
    bgColor: 'bg-[#64767e]',
    // actions: [
    //   {
    //     type: 'nextScreen',
    //   },
    // ],
    // Mode 2: Clickable input fields
    inputs: [
      {
        id: 'query',
        label: 'Query',
        expectedValue: '{ "createdAt": { "$gte": { ISODate("2025-04-25T00:00:00Z") } } }',
        clickable: true,
        x1: 20.51,
        y1: 18.79,
        x2: 67.9,
        y2: 24.9,
      },
    ],
  },
  {
    id: '009',
    title: 'Query Data',
    image: '/assets/job-simulation/mongodb-compass/011.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Find',
        x1: 86.2,
        y1: 20.01,
        x2: 89.8,
        y2: 23.61,
        left: 86.2,
        top: 20.01,
        width: 89.8 - 86.2,
        height: 23.61 - 20.01,

        action: {
          type: 'nextScreen',
        },
      },
    ],
  },
  {
    id: '010',
    title: 'Query Data Results',
    image: '/assets/job-simulation/mongodb-compass/012.png',
    bgColor: 'bg-[#64767e]',
    triggerMessage: 'I see the results',
  },
];

const getClickedButton = (screen: Screen, clickXPercent: number, clickYPercent: number) => {
  if (!screen.buttons?.length) return;
  for (const button of screen.buttons) {
    const { x1, y1, x2, y2 } = button;
    if (clickXPercent >= x1 && clickXPercent <= x2 && clickYPercent >= y1 && clickYPercent <= y2) {
      return button;
    }
  }
  return null; // No button matched
};

const getClickedInput = (screen: Screen, clickXPercent: number, clickYPercent: number) => {
  if (!screen.inputs?.length) return;
  for (const input of screen.inputs) {
    if (input.clickable && input.x1 && input.y1 && input.x2 && input.y2) {
      if (
        clickXPercent >= input.x1 &&
        clickXPercent <= input.x2 &&
        clickYPercent >= input.y1 &&
        clickYPercent <= input.y2
      ) {
        return input;
      }
    }
  }
  return null; // No input matched
};

const getNonClickableInputs = (screen: Screen) => {
  if (!screen.inputs?.length) return [];
  return screen.inputs.filter((input) => !input.clickable);
};

const MongoDBCompass = () => {
  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [showDialog, setShowDialog] = useState(false);
  const [dialogInputs, setDialogInputs] = useState<InputField[]>([]);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  // Reset input values and errors when screen changes
  useEffect(() => {
    if (screens[currentScreenIndex].triggerMessage)
      setJobsimulationTriggerMessage({
        message: screens[currentScreenIndex].triggerMessage,
        isTriggered: true,
      });

    setInputValues({});
    setValidationErrors({});
    setShowDialog(false);
  }, [currentScreenIndex]);

  const handleInputChange = (id: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [id]: value }));
    if (validationErrors[id]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Validate inputs
  const validateInputs = () => {
    const errors: Record<string, string> = {};
    let isValid = true;

    dialogInputs.forEach((input) => {
      const value = inputValues[input.id] || '';
      if (value !== input.expectedValue) {
        errors[input.id] = `Invalid value. Expected: ${input.expectedValue}`;
        isValid = false;
      }
    });

    setValidationErrors(errors);
    return isValid;
  };

  const handleDialogSubmit = () => {
    if (validateInputs()) {
      setShowDialog(false);
      setInputValues({});
      // Move to next screen
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    } else {
      // TODO: Show error
      console.log('Validation failed:', validationErrors);
    }
  };

  useEffect(() => {
    function handleResize() {
      if (imageRef.current && containerRef.current) {
        // const naturalWidth = imageRef.current.naturalWidth;
        // const naturalHeight = imageRef.current.naturalHeight;

        // const containerWidth = containerRef.current.clientWidth ?? 0;
        const containerHeight = containerRef.current.clientHeight ?? 0;

        // const scale = Math.min(containerWidth / naturalWidth, containerHeight / naturalHeight);

        // const scaledWidth = naturalWidth * scale;
        // const scaledHeight = naturalHeight * scale;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }
      }
    }

    handleResize();
  }, [imageRef.current?.src, containerRef?.current?.clientWidth, imageContainerRef]);

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const clickXPercent = ((e.clientX - rect.left) / rect.width) * 100;
    const clickYPercent = ((e.clientY - rect.top) / rect.height) * 100;

    const currentScreen = screens[currentScreenIndex];
    const screenButton = getClickedButton(currentScreen, clickXPercent, clickYPercent);
    const clickedInput = getClickedInput(currentScreen, clickXPercent, clickYPercent);

    // If click a button
    if (screenButton) {
      if (screenButton.action.type === 'nextScreen') {
        setCurrentScreenIndex((prevIndex) => prevIndex + 1);
      }
      return;
    }

    // If click an input
    if (clickedInput) {
      setDialogInputs([clickedInput]);
      setShowDialog(true);
      return;
    }

    // If the screen was clicked (not on a button or input)
    if (!screenButton && !clickedInput) {
      const nonClickableInputs = getNonClickableInputs(currentScreen);
      if (nonClickableInputs.length > 0) {
        setDialogInputs(nonClickableInputs);
        setShowDialog(true);
        return;
      }
    }

    // If the screen has a default action (like auto-advancing)
    if (currentScreen.actions?.[0]?.type === 'nextScreen') {
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    }
  };

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = screens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // useEffect(() => {
  //   if (screens[currentScreenIndex].triggerMessage)
  //     setJobsimulationTriggerMessage({
  //       message: screens[currentScreenIndex].triggerMessage,
  //       isTriggered: true,
  //     });
  // }, [currentScreenIndex, setJobsimulationTriggerMessage]);

  // State for tracking hover
  const [hoveredElement, setHoveredElement] = useState<{
    type: 'button' | 'input';
    index: number;
  } | null>(null);

  // Calculate positions for buttons and clickable inputs
  const getButtonStyles = (button: any): React.CSSProperties => {
    return {
      position: 'absolute' as const,
      left: `${button.x1}%`,
      top: `${button.y1}%`,
      width: `${button.x2 - button.x1}%`,
      height: `${button.y2 - button.y1}%`,
      cursor: 'pointer',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? 'rgba(0, 123, 255, 0.2)'
          : 'transparent',
      border:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? '2px solid rgba(0, 123, 255, 0.5)'
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto',
    };
  };

  const getInputStyles = (input: InputField, index: number): React.CSSProperties | undefined => {
    if (!input.clickable || !input.x1 || !input.y1 || !input.x2 || !input.y2) return undefined;

    return {
      position: 'absolute' as const,
      left: `${input.x1}%`,
      top: `${input.y1}%`,
      width: `${input.x2 - input.x1}%`,
      height: `${input.y2 - input.y1}%`,
      cursor: 'text',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? 'rgba(255, 193, 7, 0.2)'
          : 'transparent',
      border:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? '2px solid rgba(255, 193, 7, 0.5)'
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto', // Ensure the overlay captures mouse events
    };
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full items-center justify-center',
          screens[currentScreenIndex].bgColor ? `${screens[currentScreenIndex].bgColor}` : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {screens[currentScreenIndex].buttons?.map((button, index) => (
            <div
              key={`button-${index}`}
              style={getButtonStyles({ ...button, index })}
              onMouseEnter={() => setHoveredElement({ type: 'button', index })}
              onMouseLeave={() => setHoveredElement(null)}
              onClick={(e) => {
                e.stopPropagation();
                if (button.action.type === 'nextScreen') {
                  setCurrentScreenIndex((prevIndex) => prevIndex + 1);
                }
              }}
              title={button.title}
            />
          ))}

          {/* Overlay for inputs */}
          {screens[currentScreenIndex].inputs?.map((input, index) => {
            const style = getInputStyles(input, index);
            return input.clickable && style ? (
              <div
                key={`input-${index}`}
                style={style}
                onMouseEnter={() => setHoveredElement({ type: 'input', index })}
                onMouseLeave={() => setHoveredElement(null)}
                onClick={(e) => {
                  e.stopPropagation();
                  setDialogInputs([input]);
                  setShowDialog(true);
                }}
                title={input.label}
              />
            ) : null;
          })}
        </div>
      </div>

      {/* Dialog for dynamic inputs  */}
      <OGDialog open={showDialog} onOpenChange={setShowDialog}>
        <OGDialogContent
          title={`${screens[currentScreenIndex].title} - Input Required`}
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              {dialogInputs.length === 1
                ? `Enter ${dialogInputs[0]?.label}`
                : 'Please enter the required information'}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            {dialogInputs.map((input) => (
              <div key={input.id} className="flex flex-col gap-2">
                <Label htmlFor={input.id}>{input.label}</Label>
                <Input
                  id={input.id}
                  value={inputValues[input.id] || ''}
                  onChange={(e) => handleInputChange(input.id, e.target.value)}
                  className={cn(
                    validationErrors[input.id] ? 'border-red-500 focus:border-red-500' : '',
                    'transition-all duration-200',
                  )}
                  placeholder={`Enter ${input.label.toLowerCase()}`}
                />
                {validationErrors[input.id] && (
                  <p className="text-sm text-red-500">{validationErrors[input.id]}</p>
                )}
              </div>
            ))}
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowDialog(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleDialogSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default MongoDBCompass;
