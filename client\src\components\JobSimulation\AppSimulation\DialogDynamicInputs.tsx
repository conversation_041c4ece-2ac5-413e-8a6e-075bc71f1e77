import { AppSimulationModalConfig } from '~/common';
import {
  Button,
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
  RadioGroup,
  RadioGroupItem,
  Slider,
  TextareaAutosize,
} from '~/components';
import { formatValueWithType, getGridColumns } from './AppSimulationHelpers';

interface IDialogDynamicInputsProps {
  open: boolean;
  onOpenChange: (value: boolean) => void;
  handleModalSubmit: (value?: any) => void;
  modalConfig: AppSimulationModalConfig | null;
  setModalValues: (value: Record<string, any>) => void;
  modalValues: Record<string, any>;
}

const DialogDynamicInputs = (props: IDialogDynamicInputsProps) => {
  const { open, onOpenChange, handleModalSubmit, modalConfig, setModalValues, modalValues } = props;

  return (
    <OGDialog open={open} onOpenChange={onOpenChange}>
      <OGDialogContent
        title={modalConfig?.title || 'Modal'}
        className="max-w-[600px] bg-background text-text-primary shadow-2xl"
      >
        <OGDialogHeader>
          <OGDialogTitle>{modalConfig?.description || modalConfig?.title}</OGDialogTitle>
        </OGDialogHeader>

        <div className="flex flex-col gap-6 py-4">
          {modalConfig?.inputs.map((input) => (
            <div key={input.id} className="flex flex-col gap-2">
              <Label htmlFor={input.id}>{input.label}</Label>

              {/* Multi-Select */}
              {input.type === 'multiSelect' && input.options && (
                <div className="max-h-80 overflow-y-auto">
                  <div className={`grid ${getGridColumns(input.options.length)} gap-2`}>
                    {input.options.map((option) => (
                      <label key={option} className="flex cursor-pointer items-center space-x-2">
                        {/* TODO: using <Checkbox /> component */}
                        <input
                          type="checkbox"
                          checked={modalValues[input.id]?.includes(option) || false}
                          onChange={(e) => {
                            const currentValues = modalValues[input.id] || [];
                            if (e.target.checked) {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: [...currentValues, option],
                              }));
                            } else {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: currentValues.filter((v: string) => v !== option),
                              }));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{option}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Range Slider */}
              {input.type === 'range' && input.min !== undefined && input.max !== undefined && (
                <div className="px-2">
                  <Slider
                    value={modalValues[input.id] || [input.min, input.max]}
                    twoThumbs
                    onValueChange={(value) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: value,
                      }))
                    }
                    min={input.min}
                    max={input.max}
                    step={input.step || 1}
                    className="w-full"
                  />
                  <div className="mt-1 flex justify-between text-sm text-gray-500">
                    <span>{input.labels?.min || input.min}</span>
                    <span className="font-medium">
                      {formatValueWithType(
                        modalValues[input.id] || [input.min, input.max],
                        input.formatType,
                        input.formatConfig,
                      )}
                    </span>
                    <span>{input.labels?.max || input.max}</span>
                  </div>
                </div>
              )}

              {/* Radio Group */}
              {input.type === 'radio' && input.radioOptions && (
                <RadioGroup
                  value={modalValues[input.id] || input.defaultValue || ''}
                  onValueChange={(value) =>
                    setModalValues((prev) => ({
                      ...prev,
                      [input.id]: value,
                    }))
                  }
                >
                  {input.radioOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.value} id={`${input.id}-${option.value}`} />
                      <Label htmlFor={`${input.id}-${option.value}`} className="cursor-pointer">
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}

              {/* Text Input */}
              {input.type === 'text' && (
                <Input
                  id={input.id}
                  value={modalValues[input.id] || ''}
                  onChange={(e) =>
                    setModalValues((prev) => ({
                      ...prev,
                      [input.id]: e.target.value,
                    }))
                  }
                  placeholder={input.placeholder}
                  className="w-full"
                />
              )}

              {/* Textarea */}
              {input.type === 'textarea' && (
                <TextareaAutosize
                  id={input.id}
                  value={modalValues[input.id] || ''}
                  onChange={(e) =>
                    setModalValues((prev) => ({
                      ...prev,
                      [input.id]: e.target.value,
                    }))
                  }
                  placeholder={input.placeholder}
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              )}
            </div>
          ))}
        </div>

        <OGDialogFooter>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
            >
              Cancel
            </Button>
            <Button
              variant="submit"
              onClick={handleModalSubmit}
              className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
            >
              Apply
            </Button>
          </div>
        </OGDialogFooter>
      </OGDialogContent>
    </OGDialog>
  );
};

export default DialogDynamicInputs;
