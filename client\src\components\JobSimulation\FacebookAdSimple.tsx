import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import { AppSimulationModalConfig } from '~/common';
import {
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
  RadioGroup,
  RadioGroupItem,
  Slider,
} from '~/components';
import {
  createCalculationEngine,
  findScreenIndexById,
  formatValueWithType,
  hasTimeBasedPlaceholders,
  safeCalculateEstimates,
  uploadAppSimulationPhoto,
} from '~/components/JobSimulation/AppSimulation/AppSimulationHelpers';
import { getAppData } from '~/components/JobSimulation/AppSimulation/CalculationDataStorage';
import DialogTextInput from '~/components/JobSimulation/AppSimulation/DialogTextInput';
import LineChart from '~/components/JobSimulation/AppSimulation/LineChart';
import store from '~/store';
import { cn } from '~/utils';

// TODO: Legacy, just for demo, now use client\src\components\JobSimulation\AppSimulation\AppSimulation.tsx
const FacebookAdSimple = () => {
  console.log('::: FacebookAdSimple :::');
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  // TODO: pass appSimulationScreens and appSimulationConfig as props or fetch them from database
  const { appSimulationScreens, appSimulationConfig } = getAppData('facebook-ad')!;
  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<
    Array<{
      label: string;
      screenId: string;
      dataContext?: string;
      dataContextId?: string;
      saveToSelections?: boolean;
    }>
  >([]);
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  const [userSelections, setUserSelections] = useState<Record<string, string>>({});

  // State for data content (images and text) - now stored by data ID instead of screen ID
  const [dataContent, setDataContent] = useState<
    Record<
      string,
      {
        uploadedImage?: string;
        inputText?: string;
      }
    >
  >({});

  const [hoveredElements, setHoveredElements] = useState<number[]>([]);

  // State for text input dialog
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [textInputData, setTextInputData] = useState<{
    type: string;
    value: string;
    dataContextId: string;
    saveToSelections: boolean;
    dataContextLabel: string;
  }>({
    type: 'input',
    value: '',
    dataContextId: '',
    saveToSelections: false,
    dataContextLabel: '',
  });

  // State for dynamic modal
  const [showModal, setShowModal] = useState(false);
  const [currentModalConfig, setCurrentModalConfig] = useState<AppSimulationModalConfig | null>(
    null,
  );
  const [modalValues, setModalValues] = useState<Record<string, any>>({});

  // State for time-based placeholders
  // const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [timeBasedValues, setTimeBasedValues] = useState<Record<string, string>>({});
  const [lastUpdateTimes, setLastUpdateTimes] = useState<Record<string, number>>({});

  // Initialize Calculation Engine dynamically
  const [calculationEngine] = useState(() => {
    return createCalculationEngine(appSimulationConfig);
  });

  // Getefault values for calculation config
  const [dynamicSelections, setDynamicSelections] = useState<Record<string, any>>(() => {
    return calculationEngine?.getDefaultValues() || {};
  });
  const [calculatedEstimates, setCalculatedEstimates] = useState<Record<string, string>>({});

  // Calculate parameters change using Calculation Engine
  useEffect(() => {
    // Only skip if dynamicSelections is null/undefined, not if it's empty object
    if (!dynamicSelections || !calculationEngine || !calculationEngine.getDefaultValues()) return;

    const estimates = safeCalculateEstimates(calculationEngine, dynamicSelections);
    setCalculatedEstimates(estimates);
  }, [dynamicSelections, calculationEngine]);

  useEffect(() => {
    setShowDropdown(false);

    if (appSimulationScreens[currentScreenIndex].actions?.length) {
      for (const action of appSimulationScreens[currentScreenIndex].actions) {
        if (action.type === 'triggerMessage') {
          let finalMessage = action.message || '';

          if (action.withData && Object.keys(userSelections).length > 0) {
            const selectionsText = Object.entries(userSelections)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            finalMessage = `${finalMessage} ${selectionsText}`;
            console.log('::: finalMessage ::: ', finalMessage);
            setJobsimulationTriggerMessage({
              message: finalMessage,
              isTriggered: true,
            });
          }

          break;
        }
      }
    }

    // Init timer for screens have time-based placeholders
    if (hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex])) {
      setStartTime(Date.now());
      // setElapsedTime(0);
      setLastUpdateTimes({});
      // Initialize time-based values
      const initialValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find initial value (time = 0)
          const initialData = placeholder.dataByTime.find((data) => data.time === 0);
          if (placeholder.dataId && dataContent[placeholder.dataId]?.inputText) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.inputText!;
          } else if (initialData) {
            initialValues[placeholder.id] = initialData.value;
          }
        } else if (placeholder.increaseByTime && placeholder.increaseSettings?.from !== undefined) {
          if (placeholder.dataId && dataContent[placeholder.dataId]?.inputText) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.inputText!;
          } else {
            initialValues[placeholder.id] = placeholder.increaseSettings.from.toString();
          }
        }
      });
      setTimeBasedValues(initialValues);
    }
  }, [currentScreenIndex]);

  // Timer for time-based placeholders
  useEffect(() => {
    if (startTime === null || !hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex]))
      return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      // setElapsedTime(elapsed);

      // Update time-based values
      const updatedValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find the appropriate value for current elapsed time
          let currentValue = '0';
          for (let i = placeholder.dataByTime.length - 1; i >= 0; i--) {
            if (elapsed >= placeholder.dataByTime[i].time) {
              currentValue = placeholder.dataByTime[i].value;
              break;
            }
          }
          updatedValues[placeholder.id] = currentValue;
        } else if (placeholder.increaseByTime && placeholder.increaseSettings?.from !== undefined) {
          // Update every 10 seconds with random increase
          const currentVal = parseInt(
            timeBasedValues[placeholder.id] || placeholder.increaseSettings.from.toString(),
          );
          const lastUpdate = lastUpdateTimes[placeholder.id] || 0;
          const currentInterval = Math.floor(elapsed / 10);
          const lastInterval = Math.floor(lastUpdate / 10);

          if (elapsed >= 10 && currentInterval > lastInterval) {
            // Time for an update - add random increase
            const randomIncrease = Math.floor(Math.random() * 10) + 1;
            const newValue = currentVal + randomIncrease;
            updatedValues[placeholder.id] = newValue.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  inputText: newValue.toString(),
                },
              }));
            }
            console.log(
              `Updating ${placeholder.id}: ${currentVal} + ${randomIncrease} = ${newValue} at ${elapsed}s`,
            );
            // Update last update time
            setLastUpdateTimes((prev) => ({
              ...prev,
              [placeholder.id]: elapsed,
            }));
          } else {
            // Keep current value
            updatedValues[placeholder.id] = currentVal.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  inputText: currentVal.toString(),
                },
              }));
            }
          }
        }
      });

      setTimeBasedValues((prev) => ({ ...prev, ...updatedValues }));
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [startTime, currentScreenIndex, timeBasedValues]);

  // Handle photo upload
  const handlePhotoUpload = (contextId: string) => {
    const callbackImage = (imgUrl) => {
      // Save image data by dataId instead of screenId
      setDataContent((prev) => ({
        ...prev,
        [contextId]: {
          ...prev[contextId],
          uploadedImage: imgUrl,
        },
      }));
    };
    uploadAppSimulationPhoto({ callbackImage, width: 480, height: 480 });
  };

  // Handle text input
  const handleTextInput = (
    inputType: 'input' | 'textarea' = 'input',
    label?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    // Get current text value from dataContent using dataContextId
    setTextInputData({
      type: inputType,
      value: dataContent[dataContextId || 'unknown']?.inputText || '',
      dataContextId: dataContextId || '',
      saveToSelections: saveToSelections || false,
      dataContextLabel: label || 'Text Content',
    });
    setShowTextDialog(true);
  };

  // Handle text dialog submit
  const handleTextDialogSubmit = () => {
    if (textInputData?.saveToSelections && textInputData.dataContextId) {
      setDataContent((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']: {
          ...prev[textInputData.dataContextId || 'unknown'],
          inputText: textInputData.value,
        },
      }));

      setUserSelections((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']:
          `${textInputData.dataContextLabel || 'Text'}: "${textInputData.value}"`,
      }));
    }

    setShowTextDialog(false);
    setTextInputData({
      type: 'input',
      value: '',
      dataContextId: '',
      saveToSelections: false,
      dataContextLabel: '',
    });
  };

  // Handle dynamic modal submit
  const handleModalSubmit = () => {
    if (!currentModalConfig) return;

    console.log('Modal submit - current values:', modalValues);

    const updatedDataContent: Record<string, any> = {};
    const updatedUserSelections: Record<string, string> = {};

    currentModalConfig.inputs.forEach((input) => {
      const value = modalValues[input.id];
      if (value !== undefined && input.dataId) {
        let formattedValue = '';

        switch (input.type) {
          case 'multiSelect':
            formattedValue = Array.isArray(value) ? value.join(', ') : '';
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: Array.isArray(value) ? value : [],
            }));
            break;
          case 'range':
            if (Array.isArray(value)) {
              formattedValue = formatValueWithType(value, input.formatType, input.formatConfig);
            }
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            if (Array.isArray(value)) {
              setDynamicSelections((prev) => ({
                ...prev,
                [input.dataId]: [value[0], value[1]],
              }));
            }
            break;
          case 'radio':
          case 'text':
          case 'textarea':
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: String(value),
            }));
            break;
          default:
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
        }

        updatedDataContent[input.dataId] = {
          inputText: formattedValue,
        };
      }
    });

    // Update placeholders with selected data
    setDataContent((prev) => ({
      ...prev,
      ...updatedDataContent,
    }));

    // Update user selections
    setUserSelections((prev) => ({
      ...prev,
      ...updatedUserSelections,
    }));

    setShowModal(false);
    setCurrentModalConfig(null);
    setModalValues({});
  };

  useEffect(() => {
    function handleResize() {
      if (imageRef.current && containerRef.current) {
        const containerHeight = containerRef.current.clientHeight ?? 0;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }
      }
    }

    handleResize();
  }, [imageRef.current?.src, containerRef?.current?.clientWidth, imageContainerRef]);

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const screenElements = appSimulationScreens[currentScreenIndex]?.elements || [];
    const indexes = screenElements.map((_, index) => index);
    setHoveredElements(indexes);
  };

  const handleDropdownOptionClick = (
    screenId: string,
    optionLabel: string,
    dataContext?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
    value?: number,
  ) => {
    const targetIndex = findScreenIndexById(appSimulationScreens, screenId);
    if (targetIndex !== -1) {
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection if saveToSelections is true
    if (saveToSelections && dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: dataContext || optionLabel,
      }));
    }

    // Update dynamic selections for calculation
    if (dataContextId && value !== undefined) {
      setDynamicSelections((prev) => ({
        ...prev,
        [dataContextId]: value,
      }));
    }

    setShowDropdown(false);
  };

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = appSimulationScreens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // Calculate positions for buttons and clickable inputs
  const getElementStyles = (button: any): React.CSSProperties => {
    return {
      position: 'absolute' as const,
      left: `${button.x1}%`,
      top: `${button.y1}%`,
      width: `${button.x2 - button.x1}%`,
      height: `${button.y2 - button.y1}%`,
      cursor: 'pointer',
      zIndex: 10,
      backgroundColor: hoveredElements.includes(button.index)
        ? (button.backgroundColor ?? 'rgba(0, 123, 255, 0.2)')
        : 'transparent',
      border: hoveredElements.includes(button.index)
        ? (button.border ?? '2px solid rgba(0, 123, 255, 0.5)')
        : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto',
    };
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full flex-col items-center justify-center',
          appSimulationScreens[currentScreenIndex].bgColor
            ? `${appSimulationScreens[currentScreenIndex].bgColor}`
            : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {appSimulationScreens[currentScreenIndex].elements?.map((element, index) => (
            <div
              key={`element-${appSimulationScreens[currentScreenIndex].id}-${index}`}
              style={getElementStyles({ ...element, index })}
              onMouseEnter={() => setHoveredElements([index])}
              onMouseLeave={() => setHoveredElements([])}
              onClick={(e) => {
                e.stopPropagation();
                const arrayButtonAction =
                  element.actions || (element.action ? [element.action] : []) || [];
                console.log('arrayButtonAction ::: ', arrayButtonAction);
                for (const buttonAction of arrayButtonAction) {
                  console.log('arrayButtonAction ::: buttonAction ::: ', buttonAction);
                  if (!buttonAction) continue;
                  if (buttonAction.type === 'nextScreen') {
                    if (buttonAction.screenId) {
                      const targetIndex = findScreenIndexById(
                        appSimulationScreens,
                        buttonAction.screenId,
                      );
                      if (targetIndex !== -1) {
                        setCurrentScreenIndex(targetIndex);
                      }
                    } else {
                      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
                    }
                  } else if (buttonAction.type === 'dropdown' && buttonAction.dropdownOptions) {
                    // Show dropdown
                    setDropdownOptions(buttonAction.dropdownOptions);
                    setDropdownPosition({
                      x: (element.x1 + element.x2) / 2,
                      y: element.y2 + 2,
                    });
                    setShowDropdown(true);
                  } else if (buttonAction.type === 'uploadPhoto') {
                    // Handle photo upload
                    handlePhotoUpload(buttonAction.dataContextId || 'image');
                  } else if (buttonAction.type === 'inputText') {
                    // Handle text input
                    handleTextInput(
                      buttonAction.inputTextType,
                      buttonAction.dataContextLabel,
                      buttonAction.dataContextId,
                      buttonAction.saveToSelections,
                    );
                  } else if (buttonAction.type === 'modal') {
                    // Handle dynamic modal
                    if (buttonAction.modalConfig) {
                      setCurrentModalConfig(buttonAction.modalConfig);
                      // Initialize modal values with current selections or defaults
                      const initialValues: Record<string, any> = {};
                      buttonAction.modalConfig.inputs.forEach((input) => {
                        // Generic initialization based on dataId
                        const currentValue = dynamicSelections[input.dataId!];
                        if (currentValue !== undefined) {
                          initialValues[input.id] = currentValue;
                        } else if (input.defaultValue !== undefined) {
                          initialValues[input.id] = input.defaultValue;
                        } else if (input.type === 'multiSelect') {
                          initialValues[input.id] = [];
                        } else if (
                          input.type === 'range' &&
                          input.min !== undefined &&
                          input.max !== undefined
                        ) {
                          initialValues[input.id] = [input.min, input.max];
                        } else {
                          initialValues[input.id] = '';
                        }
                      });
                      console.log('Modal initialized with values:', initialValues);
                      setModalValues(initialValues);
                      setShowModal(true);
                    }
                  } else if (buttonAction.type === 'triggerMessage') {
                    // Handle trigger message
                    let finalMessage = buttonAction.message || '';

                    if (buttonAction.withData && Object.keys(userSelections).length > 0) {
                      const selectionsText = Object.entries(userSelections)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(', ');
                      finalMessage = `${finalMessage} ${selectionsText}`;
                    }

                    console.log('finalMessage ::: 2 ', finalMessage);

                    setJobsimulationTriggerMessage({
                      message: finalMessage,
                      isTriggered: true,
                    });
                  }
                }
              }}
              title={element.title}
            />
          ))}

          {/* Placeholders overlay */}
          {appSimulationScreens[currentScreenIndex].placeholders?.map((placeholder, index) => {
            // Get data content by dataId instead of screenId
            const currentContent = placeholder.dataId ? dataContent[placeholder.dataId] : undefined;

            // Check if this placeholder has time-based data
            const hasTimeBasedData = placeholder.dataByTime || placeholder.increaseByTime;
            const timeBasedValue = hasTimeBasedData
              ? (dataContent[placeholder.dataId || '']?.inputText ??
                timeBasedValues[placeholder.id])
              : undefined;

            if (placeholder.type === 'image' && currentContent?.uploadedImage) {
              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    height: `${placeholder.y2 - placeholder.y1}%`,
                    zIndex: 15,
                    overflow: 'hidden',
                    borderRadius: '4px',
                  }}
                >
                  <img
                    src={currentContent.uploadedImage}
                    alt="Uploaded content"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              );
            }

            if (placeholder.type === 'text') {
              // Determine what text to display
              let displayText = '';
              if (hasTimeBasedData && timeBasedValue) {
                displayText = timeBasedValue;
              } else if (placeholder.dataId && calculatedEstimates[placeholder.dataId]) {
                // Use calculated estimates for specific dataIds
                displayText = calculatedEstimates[placeholder.dataId];
              } else if (currentContent?.inputText) {
                displayText = currentContent.inputText;
              } else if (currentContent?.inputText === undefined && placeholder.initialValue) {
                displayText = placeholder.initialValue;
              }

              if (displayText) {
                return (
                  <div
                    key={`placeholder-${placeholder.id || index}`}
                    style={{
                      position: 'absolute',
                      left: `${placeholder.x1}%`,
                      top: `${placeholder.y1}%`,
                      width: `${placeholder.x2 - placeholder.x1}%`,
                      height: `${placeholder.y2 - placeholder.y1}%`,
                      zIndex: 15,
                      display: 'flex',
                      justifyContent: 'left',
                      alignItems: 'center',
                      backgroundColor: hasTimeBasedData
                        ? 'rgba(59, 130, 246, 0.1)'
                        : 'rgba(255, 255, 255, 0.9)',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      lineHeight: '0.75rem',
                      padding: '4px 2px 2px 2px',
                      color: hasTimeBasedData ? '#1e40af' : '#333',
                      textAlign: 'left',
                      overflow: 'auto',
                      wordBreak: 'break-word',
                      whiteSpace: 'pre-line',
                      fontWeight: hasTimeBasedData ? 'bold' : 'normal',
                      border: hasTimeBasedData ? '1px solid rgba(59, 130, 246, 0.3)' : 'none',
                      ...placeholder.style,
                    }}
                  >
                    {displayText}
                  </div>
                );
              }
            }

            return null;
          })}

          {/* Charts overlay */}
          {appSimulationScreens[currentScreenIndex].charts?.map((chart, index) => {
            if (chart.type === 'line') {
              return <LineChart key={`chart-${chart.id || index}`} data={chart.data} />;
            }
            return null;
          })}

          {/* Dropdown overlay */}
          {showDropdown && (
            <div
              style={{
                position: 'absolute',
                left: `${dropdownPosition.x}%`,
                top: `${dropdownPosition.y}%`,
                transform: 'translateX(-50%)',
                zIndex: 20,
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                minWidth: '150px',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {dropdownOptions.map((option, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    borderBottom: index < dropdownOptions.length - 1 ? '1px solid #eee' : 'none',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                  onClick={() => {
                    handleDropdownOptionClick(
                      option.screenId,
                      option.label,
                      option.dataContext,
                      option.dataContextId,
                      option.saveToSelections,
                      (option as any).value,
                    );
                  }}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* TODO: find a way to merge DialogTextInput with Dyanimic Modal --> Use Dynamic Modal */}

      <DialogTextInput
        isOpen={showTextDialog}
        onOpenChange={setShowTextDialog}
        textInputData={textInputData}
        onInputChange={(value) => setTextInputData((prev) => ({ ...prev, value }))}
        onSubmit={handleTextDialogSubmit}
      />

      {/* Dynamic Modal */}
      <OGDialog open={showModal} onOpenChange={setShowModal}>
        <OGDialogContent
          title={currentModalConfig?.title || 'Modal'}
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              {currentModalConfig?.description || currentModalConfig?.title}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-6 py-4">
            {currentModalConfig?.inputs.map((input) => (
              <div key={input.id} className="flex flex-col gap-2">
                <Label htmlFor={input.id}>{input.label}</Label>

                {/* Multi-Select */}
                {input.type === 'multiSelect' && input.options && (
                  <div className="space-y-2">
                    {input.options.map((option) => (
                      <label key={option} className="flex cursor-pointer items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={modalValues[input.id]?.includes(option) || false}
                          onChange={(e) => {
                            const currentValues = modalValues[input.id] || [];
                            if (e.target.checked) {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: [...currentValues, option],
                              }));
                            } else {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: currentValues.filter((v: string) => v !== option),
                              }));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{option}</span>
                      </label>
                    ))}
                  </div>
                )}

                {/* Range Slider */}
                {input.type === 'range' && input.min !== undefined && input.max !== undefined && (
                  <div className="px-2">
                    <Slider
                      value={modalValues[input.id] || [input.min, input.max]}
                      onValueChange={(value) =>
                        setModalValues((prev) => ({
                          ...prev,
                          [input.id]: value,
                        }))
                      }
                      min={input.min}
                      max={input.max}
                      step={input.step || 1}
                      className="w-full"
                    />
                    <div className="mt-1 flex justify-between text-sm text-gray-500">
                      <span>{input.labels?.min || input.min}</span>
                      <span className="font-medium">
                        {formatValueWithType(
                          modalValues[input.id] || [input.min, input.max],
                          input.formatType,
                          input.formatConfig,
                        )}
                      </span>
                      <span>{input.labels?.max || input.max}</span>
                    </div>
                  </div>
                )}

                {/* Radio Group */}
                {input.type === 'radio' && input.radioOptions && (
                  <RadioGroup
                    value={modalValues[input.id] || input.defaultValue || ''}
                    onValueChange={(value) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: value,
                      }))
                    }
                  >
                    {input.radioOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} id={`${input.id}-${option.value}`} />
                        <Label htmlFor={`${input.id}-${option.value}`} className="cursor-pointer">
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                )}

                {/* Text Input */}
                {input.type === 'text' && (
                  <Input
                    id={input.id}
                    value={modalValues[input.id] || ''}
                    onChange={(e) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: e.target.value,
                      }))
                    }
                    placeholder={input.placeholder}
                    className="w-full"
                  />
                )}

                {/* Textarea */}
                {input.type === 'textarea' && (
                  <textarea
                    id={input.id}
                    value={modalValues[input.id] || ''}
                    onChange={(e) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: e.target.value,
                      }))
                    }
                    placeholder={input.placeholder}
                    className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    rows={5}
                  />
                )}
              </div>
            ))}
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowModal(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleModalSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                Apply
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default FacebookAdSimple;
