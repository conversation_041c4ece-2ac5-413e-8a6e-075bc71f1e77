import { useState, useId, useContext } from 'react';
import * as Ariakit from '@ariakit/react';
import { Settings, Moon, Sun, LogOut } from 'lucide-react';
import type * as t from '~/common';
import { DropdownPopup, TooltipAnchor } from '~/components/ui';
import { useMediaQuery, useAuthContext, ThemeContext } from '~/hooks';
import JobSimulationSettingDialog from '../JobSimulationSettingDialog';

export default function JobSimulationHeaderSettings() {
  const [isPopoverActive, setIsPopoverActive] = useState(false);
  const [isOpenSettingDialog, setIsOpenSettingDialog] = useState(false);
  const { theme, setTheme } = useContext(ThemeContext);
  const { logout } = useAuthContext();

  const menuId = useId();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');

  const settingsHandler = () => {
    setIsOpenSettingDialog(true);
    setIsPopoverActive(false);
  };

  const themeHandler = () => {
    const nextTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(nextTheme);
  };

  const logoutHandler = () => {
    logout();
  };

  const dropdownItems: t.MenuItemProps[] = [
    {
      label: 'Settings',
      onClick: settingsHandler,
      icon: <Settings className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: false,
    },
    {
      label: theme === 'dark' ? 'Light Theme' : 'Dark Theme',
      onClick: themeHandler,
      icon: {
        dark: <Sun className="icon-md mr-2 text-text-secondary" />,
        light: <Moon className="icon-md mr-2 text-text-secondary" />
      }[theme],
      hideOnClick: true,
    },
    {
      label: 'Logout',
      onClick: logoutHandler,
      icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
  ];

  return (
    <>
      <DropdownPopup
        menuId={menuId}
        focusLoop={true}
        isOpen={isPopoverActive}
        setIsOpen={setIsPopoverActive}
        trigger={
          <TooltipAnchor
            description="Job Simulation Settings"
            render={
              <Ariakit.MenuButton
                id="settings-menu-button"
                aria-label="Settings options"
                className="inline-flex size-5 flex-shrink-0 items-center justify-center rounded-lg bg-transparent text-text-primary transition-all ease-in-out hover:bg-surface-tertiary disabled:pointer-events-none disabled:opacity-50 radix-state-open:bg-surface-tertiary dark:text-white"
              >
                <Settings
                  className="h-5 w-5 cursor-pointer"
                  aria-hidden="true"
                  focusable="false"
                />
              </Ariakit.MenuButton>
            }
          />
        }
        items={dropdownItems}
        className={isSmallScreen ? '' : 'absolute right-0 top-0 mt-2'}
      />

      {isOpenSettingDialog && (
        <JobSimulationSettingDialog
          isOpenDialog={isOpenSettingDialog}
          closeDialog={() => setIsOpenSettingDialog(false)}
        />
      )}
    </>
  );
}
