import React, { useState } from 'react';
import { Button } from '~/components/ui';

interface DateRangeDropdownProps {
	selectedRange: string;
	onSelect: (range: string) => void;
	onClose: () => void;
}

const DateRangeDropdown: React.FC<DateRangeDropdownProps> = ({
	selectedRange,
	onSelect,
	onClose,
}) => {
	const [showCalendar, setShowCalendar] = useState(false);
	const [customStartDate, setCustomStartDate] = useState<Date>();
	const [customEndDate, setCustomEndDate] = useState<Date>();

	const dateRanges = [
		'Last 7 days',
		'Last 30 days',
		'Last 60 days',
		'Last 90 days',
		'This year',
		'Custom'
	];

	const handleRangeSelect = (range: string) => {
		if (range === 'Custom') {
			setShowCalendar(true);
		} else {
			onSelect(range);
		}
	};

	const handleCustomDateConfirm = () => {
		if (customStartDate && customEndDate) {
			onSelect(`${customStartDate.toLocaleDateString()} - ${customEndDate.toLocaleDateString()}`);
			setShowCalendar(false);
		}
	};

	return (
		<div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
			{!showCalendar ? (
				<div className="p-4">
					<h3 className="font-medium text-gray-800 mb-3">Select Date Range</h3>
					<div className="space-y-2">
						{dateRanges.map((range) => (
							<label key={range} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded">
								<input
									type="radio"
									name="dateRange"
									checked={selectedRange === range}
									onChange={() => handleRangeSelect(range)}
									className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
								/>
								<span className="text-sm text-gray-700">{range}</span>
							</label>
						))}
					</div>
				</div>
			) : (
				<div className="p-4">
					<h3 className="font-medium text-gray-800 mb-3">Custom Date Range</h3>
					<div className="space-y-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
							Calendar
							{/* <Calendar
								mode="single"
								selected={customStartDate}
								onSelect={setCustomStartDate}
								className="rounded-md border"
							/> */}
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
							Calendar
							{/* <Calendar
								mode="single"
								selected={customEndDate}
								onSelect={setCustomEndDate}
								className="rounded-md border"
							/> */}
						</div>
						<div className="flex justify-end space-x-2">
							<Button variant="outline" onClick={() => setShowCalendar(false)}>
								Cancel
							</Button>
							<Button onClick={handleCustomDateConfirm}>
								Confirm
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default DateRangeDropdown;