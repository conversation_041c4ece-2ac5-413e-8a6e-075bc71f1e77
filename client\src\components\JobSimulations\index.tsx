import _filter from 'lodash/filter';
import _get from 'lodash/get';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import {
	useAdminJobSimulationList
} from '~/data-provider/JobSimulation/queries';
import JobSimulationSettingDialog from '../JobSimulation/JobSimulationSettingDialog';
import { Button, Dialog, DialogContent, DialogHeader, DialogTitle, Input, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui';
export default function JobSimulationsPage() {
	const [search, setSearch] = useState("");
	const [isLogoDialogOpen, setIsLogoDialogOpen] = useState(false);
	const [selectedJobSimulationId, setSelectedJobSimulationId] = useState<string | null>(null);

	const [isCredentialDialogOpen, setIsCredentialDialogOpen] = useState(false);
	const [credentials, setCredentials] = useState({ username: '', password: '' });
	const { data = [], isLoading } = useAdminJobSimulationList();
	console.log("🚀 ~ JobSimulationsPage ~ data:", data)

	const filteredData = _filter(_get(data, 'data', []), (item) =>
		item.name.toLowerCase().includes(search.toLowerCase())
	);

	const [editedCredentials, setEditedCredentials] = useState({ username: '', password: '' });

	useEffect(() => {
		if (isCredentialDialogOpen) {
			setEditedCredentials(credentials);
		}
	}, [isCredentialDialogOpen, credentials]);


	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-4">
				<Input
					placeholder="Search"
					value={search}
					onChange={(e) => setSearch(e.target.value)}
					className="max-w-xs w-full"
				/>
				<Link to='/job-simulations/create'>
					<Button>Create</Button>
				</Link>
			</div>

			<div className="overflow-hidden rounded-md border border-border">
				<Table className="w-full">
					<TableHeader>
						<TableRow>
							<TableHead>Name</TableHead>
							<TableHead>Logo</TableHead>
							<TableHead>Credentials</TableHead>
							<TableHead>Participants</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{(isLoading ? Array.from({ length: 3 }) : filteredData).map((item, idx) => (
							<TableRow key={idx} className="border-b border-border">
								<TableCell className="border-border">
									{isLoading ? (
										<div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
									) : (
										<Link to={`/job-simulations/${item.jobSimulationId}/edit`}>
											<p className="underline text-left hover:text-blue-500">{item.name}</p>
										</Link>
									)}
								</TableCell>
								<TableCell className="border-border">
									{isLoading ? (
										<div className="h-10 w-10 bg-muted animate-pulse rounded" />
									) : (
										<button
											onClick={() => {
												setSelectedJobSimulationId(item.jobSimulationId);
												setIsLogoDialogOpen(true);
											}}
											className="rounded hover:ring-2 max-w-[175px]"
										>
											<img src={item.logo} alt="logo" className="w-full h-full object-contain" />
										</button>
									)}
								</TableCell>
								<TableCell className="border-border">
									{isLoading ? (
										<div className="h-4 w-24 bg-muted animate-pulse rounded" />
									) : (
										<button
											className="text-blue-500 underline"
											onClick={() => {
												setSelectedJobSimulationId(item.id);
												setCredentials({
													username: item.credential.username, password: item.credential.password
												});
												setIsCredentialDialogOpen(true);
											}}
										>
											username/password
										</button>
									)}
								</TableCell>
								<TableCell className="border-border">
									{isLoading ? (
										<div className="h-4 w-6 bg-muted animate-pulse rounded" />
									) : (
										item.participants
									)}
								</TableCell>
								<TableCell className="text-right text-sm text-muted-foreground border-border">
									{isLoading ? (
										<div className="h-4 w-12 ml-auto bg-muted animate-pulse rounded" />
									) : (
										<span className="cursor-not-allowed opacity-60">Delete</span>
									)}
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* <div className="flex justify-end gap-2 mt-4">
				<Button>1</Button>
				<Button>2</Button>
				<Button>3</Button>
			</div> */}
			{selectedJobSimulationId && (
				<JobSimulationSettingDialog
					isOpenDialog={isLogoDialogOpen}
					closeDialog={() => setIsLogoDialogOpen(false)}
					jobId={selectedJobSimulationId}
				/>
			)}
			<Dialog open={isCredentialDialogOpen} onOpenChange={setIsCredentialDialogOpen}>
				<DialogContent className="max-w-xl w-full pb-2">
					<DialogHeader>
						<DialogTitle>Update Credentials</DialogTitle>
					</DialogHeader>
					<div className="space-y-4 py-2 p-4">
						<Input
							placeholder="Username"
							value={credentials.username}
							onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
						/>
						<Input
							placeholder="Password"
							type="password"
							value={editedCredentials.password}
							onChange={(e) =>
								setEditedCredentials({ ...editedCredentials, password: e.target.value })
							}
						/>
						<div className="flex justify-end gap-2">
							<Button onClick={() => setIsCredentialDialogOpen(false)}>
								Cancel
							</Button>
							<Button
								onClick={() => {
									// Submit updated credentials here
									console.log('Submitting:', editedCredentials);
									setIsCredentialDialogOpen(false);
								}}
							>
								Save
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
