import { memo, useEffect, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import store from '~/store';
import { Button, Input } from '../ui';
import { cn } from '~/utils';

const FloatingMessage = ({ message, className }: { message: any; className: string }) => {
  return (
    <div
      key={message.id}
      className={cn(
        'pointer-events-auto max-w-md rounded-2xl px-6 py-4 shadow-lg',
        className,
        message.isUser
          ? 'ml-auto mr-6 border-2 border-blue-600 bg-blue-600/50 text-white'
          : 'ml-6 mr-auto border-2 border-purple-800 bg-purple-800/50 text-white',
      )}
    >
      <p className="text-sm md:text-base">{message.text}</p>
    </div>
  );
};

const FloatingChatMessages = ({
  messages,
  removingMessages,
}: {
  messages: any[];
  removingMessages: any[];
}) => {
  // const [messages, setMessages] = useState<any[]>([]);
  // console.log('List messages ::: ', messages);
  return (
    <div className="pointer-events-none fixed left-0 right-0 top-0 z-50 flex flex-col items-center pt-20">
      <div className="flex w-full max-w-2xl flex-col gap-3">
        {removingMessages?.map((message) => (
          <FloatingMessage
            key={message.id}
            message={message}
            className="message-animation-fadeout-now"
          />
        ))}

        {messages?.map((message) => (
          <FloatingMessage key={message.id} message={message} className="message-animation" />
          // <div
          //   key={message.id}
          //   className={cn(
          //     'message-animation pointer-events-auto max-w-md rounded-2xl px-6 py-4 shadow-lg',
          //     message.isUser
          //       ? 'ml-auto mr-6 border-2 border-blue-600 bg-blue-600/50 text-white'
          //       : 'ml-6 mr-auto border-2 border-purple-800 bg-purple-800/50 text-white',
          //   )}
          // >
          //   <p className="text-sm md:text-base">{message.text}</p>
          // </div>
        ))}
      </div>
    </div>
  );
};

function VirtualWorld({ url }: { url: string }) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [command, setCommand] = useState('');
  const [commandType] = useState('avatar');
  const [isReady, setIsReady] = useState(false);
  // const [response, setResponse] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [removingMessages, setRemovingMessages] = useState<any[]>([]);
  const [virtualWorldMessage, setVirtualWorldMessage] = useRecoilState(
    store.jobSimulationVirtualWorldMessage,
  );

  // Send command to the iframe
  const sendCommand = (msg: string) => {
    if (!msg.trim() || !isReady || !iframeRef.current) {
      // console.log('Cannot send command: ', {
      //   commandEmpty: !msg.trim(),
      //   notReady: !isReady,
      //   noIframeRef: !iframeRef.current,
      // });
      return;
    }

    // Create message payload
    const messageData = {
      type: 'command',
      command: msg.trim(),
      commandType: commandType,
      requestId: `req_${Date.now()}`,
    };

    // console.log('Sending command to iframe:', messageData);

    try {
      // Send the message to the iframe
      iframeRef.current.contentWindow?.postMessage(messageData, 'https://space.agentos.cloud');
      // console.log('Command sent successfully');
    } catch (error) {
      console.error('Error sending message to iframe:', error);
    }
  };
  const addMessage = (text: string, isUser = true) => {
    const newMessage = {
      id: Date.now(),
      text,
      isUser,
      timer: null,
    };

    if (messages.length === 2) {
      const removingMessage = { ...messages[0] };
      removingMessage.isRemoving = true;
      if (removingMessage.timer) {
        clearTimeout(removingMessage.timer);
      }
      removingMessage.timer = setTimeout(() => {
        setRemovingMessages((prev) => prev.filter((msg) => msg.id !== removingMessage.id));
      }, 1000);
      setRemovingMessages([removingMessage]);
    }

    // Set timeout to remove the message after 10 seconds with fade out
    let timer = setTimeout(() => {
      setMessages((prev) => prev.filter((msg) => msg.id !== newMessage.id));
    }, 10000);

    (newMessage.timer as any) = timer;

    // Keep only the last 3 messages
    setMessages((prev) => {
      const newMessages = [...prev, newMessage].slice(-2);
      return newMessages;
    });
  };

  // Listen for messages from the iframe
  useEffect(() => {
    const handleMessage = (event) => {
      // console.log('Parent received message:', event.origin, event.data);

      // Accept messages from the iframe's origin
      if (event.origin !== 'https://space.agentos.cloud') {
        // console.log('Skipping message from different origin:', event.origin);
        return;
      }

      // Handle "ready" message from the iframe
      if (event.data?.type === 'iframe:ready') {
        // console.log('3D App is ready to receive commands!');
        setIsReady(true);
      }

      // Handle response messages
      if (event.data?.type === 'response') {
        // console.log('Received response from iframe:', event.data);
        if (event.data?.message) {
          addMessage(event.data.message, false);
        }
        // setResponse(event.data);
      }
    };

    window.addEventListener('message', handleMessage);
    // console.log('Parent: Added message event listener');

    // Try to detect if iframe is already loaded
    if (iframeRef.current && iframeRef.current.contentWindow) {
      console.log('Parent: Iframe reference exists, checking if already loaded');

      // Attempt to ping the iframe to check if it's ready
      try {
        iframeRef.current.contentWindow.postMessage(
          { type: 'ping' },
          'https://space.agentos.cloud',
        );
        console.log('Parent: Sent ping to iframe');
      } catch (error) {
        console.warn('Parent: Failed to ping iframe', error);
      }
    }

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener('message', handleMessage);
      // console.log('Parent: Removed message event listener');
    };
  }, [iframeRef, messages]);

  useEffect(() => {
    if (!virtualWorldMessage) return;
    sendCommand(virtualWorldMessage);
    addMessage(virtualWorldMessage, true);
    setVirtualWorldMessage(null);
  }, [virtualWorldMessage]);

  return (
    <>
      {/* <div className="absolute bottom-0 left-0">
        <Input
          id="chatGptLabel"
          value={command}
          onChange={(e) => setCommand(e.target.value || '')}
          className="max-w-52"
        />
        <Button variant={'ghost'} onClick={() => sendCommand(command)}>
          Send Message
        </Button>
      </div> */}
      <FloatingChatMessages messages={messages} removingMessages={removingMessages} />
      <iframe
        ref={iframeRef}
        src={url}
        className="h-full w-full border-0"
        allow="camera; microphone; display-capture; clipboard-read; clipboard-write; fullscreen"
      />
    </>
  );
}

export default memo(VirtualWorld);

// Add necessary CSS for fade animations
const style = document.createElement('style');
style.textContent = `
  @keyframes msgFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes msgFadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
  }

  @keyframes msgFadeOut2 {
    from { opacity: 1; transform: translateY(0);}
    to { opacity: 0; transform: translateY(-10px); }
  }
  
  .message-animation {
    animation: msgFadeIn 0.3s ease-out forwards, msgFadeOut 0.5s ease-in forwards;
    animation-delay: 0s, 9.5s;
  }

  .message-animation-fadeout-now {
    animation: msgFadeOut2 1.5s;
    animation-delay: 0s;
  }
`;
document.head.appendChild(style);
