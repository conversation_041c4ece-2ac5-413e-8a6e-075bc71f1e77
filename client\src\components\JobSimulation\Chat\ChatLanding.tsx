import { easings } from '@react-spring/web';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys, TConversation } from 'librechat-data-provider';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { useAgentsMapContext, useAssistantsMapContext, useChatContext } from '~/Providers';
import { TJobSimulationContext } from '~/common';
import { Progress, SplitText } from '~/components';
import ConvoIcon from '~/components/Endpoints/ConvoIcon';
import { useGetConvoIdQuery, useGetEndpointsQuery } from '~/data-provider';
import { useSubmitMessage } from '~/hooks';
import store from '~/store';

const containerClassName =
  'shadow-stroke relative flex h-full items-center justify-center rounded-full bg-white text-black';

export default function ChatLanding({
  centerFormOnLanding,
  name,
  description,
  isLoadingMessages,
  index = 0,
  setIsChatReady,
}: {
  centerFormOnLanding: boolean;
  name: string;
  description: string;
  isLoadingMessages: boolean;
  index?: number;
  setIsChatReady: (isChatReady: boolean) => void;
}) {
  const { useCreateConversationAtom } = store;
  const { conversation } = useCreateConversationAtom(index);

  const agentsMap = useAgentsMapContext();
  const assistantMap = useAssistantsMapContext();
  const { data: endpointsConfig } = useGetEndpointsQuery();

  const [textHasMultipleLines, setTextHasMultipleLines] = useState(false);
  const [lineCount, setLineCount] = useState(1);
  const [contentHeight, setContentHeight] = useState(0);
  const contentRef = useRef<HTMLDivElement>(null);

  const handleLineCountChange = useCallback((count: number) => {
    setTextHasMultipleLines(count > 1);
    setLineCount(count);
  }, []);

  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.offsetHeight);
    }
  }, [lineCount, description]);

  const getDynamicMargin = useMemo(() => {
    let margin = 'mb-0';

    if (lineCount > 2 || (description && description.length > 100)) {
      margin = 'mb-10';
    } else if (lineCount > 1 || (description && description.length > 0)) {
      margin = 'mb-6';
    } else if (textHasMultipleLines) {
      margin = 'mb-4';
    }

    if (contentHeight > 200) {
      margin = 'mb-16';
    } else if (contentHeight > 150) {
      margin = 'mb-12';
    }

    return margin;
  }, [lineCount, description, textHasMultipleLines, contentHeight]);

  return (
    <div
      className={`flex h-full transform-gpu flex-col items-center justify-center pb-16 transition-all duration-200 ${centerFormOnLanding ? 'max-h-full sm:max-h-0' : 'max-h-full'} ${getDynamicMargin}`}
    >
      <div ref={contentRef} className="flex flex-col items-center gap-0 p-2">
        <div
          className={`flex ${textHasMultipleLines ? 'flex-col' : 'flex-col md:flex-col'} items-center justify-center gap-4`}
        >
          <div className={`relative size-80 justify-center ${textHasMultipleLines ? 'mb-2' : ''}`}>
            <ConvoIcon
              agentsMap={agentsMap}
              assistantMap={assistantMap}
              conversation={conversation!}
              endpointsConfig={endpointsConfig}
              containerClassName={containerClassName}
              context="landing"
              className="h-2/3 w-2/3"
              size={41}
            />
          </div>
          <div className="flex flex-col items-center gap-0 p-2">
            <SplitText
              key={`split-text-${name}`}
              text={name}
              className="text-4xl font-medium text-text-primary"
              delay={50}
              textAlign="center"
              animationFrom={{ opacity: 0, transform: 'translate3d(0,50px,0)' }}
              animationTo={{ opacity: 1, transform: 'translate3d(0,0,0)' }}
              easing={easings.easeOutCubic}
              threshold={0}
              rootMargin="0px"
              onLineCountChange={handleLineCountChange}
            />
          </div>
        </div>
        <div>
          <div
            className="animate-fadeIn mt-2 max-w-md whitespace-pre-line text-center text-lg font-normal text-text-primary"
            dangerouslySetInnerHTML={{ __html: description }}
          ></div>
          <div className="mt-4 w-full max-w-md">
            <LoadingBar isLoadingMessages={isLoadingMessages} setIsChatReady={setIsChatReady} />
          </div>
        </div>
      </div>
    </div>
  );
}

// TODO: sync loading time with ChatFormAuto first message
function LoadingBar({
  isLoadingMessages,
  setIsChatReady,
}: {
  isLoadingMessages: boolean;
  setIsChatReady: (isChatReady: boolean) => void;
}) {
  const { jobSimulationData } = useOutletContext<TJobSimulationContext>();
  // const { submitMessage } = useSubmitMessage();
  const [progressPercent, setProgressPercent] = useState(0);
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  useEffect(() => {
    setProgressPercent(0);

    const interval = setInterval(() => {
      setProgressPercent((prev) => {
        const increment = 0.5 + (85 - prev) * 0.05;
        return Math.min(prev + increment, 85);
      });
    }, 100);

    const timer1 = setTimeout(() => {
      clearInterval(interval);
      setProgressPercent(90);
    }, 4000);

    // const timer2 = setTimeout(() => {
    //   setProgressPercent(100);

    //   // setTimeout(() => {
    //   //   setShowLoading(false);
    //   // }, 200);
    // }, 5000);

    return () => {
      clearInterval(interval);
      clearTimeout(timer1);
      // clearTimeout(timer2);
    };
  }, []);

  useEffect(() => {
    if (!isLoadingMessages && jobSimulationData?.jobSimulationId && progressPercent >= 90) {
      setProgressPercent(100);
    }
  }, [
    jobSimulationData?.jobSimulationId,
    jobSimulationData?.progress?.conversationId,
    progressPercent,
    isLoadingMessages,
  ]);

  useEffect(() => {
    if (!jobSimulationData || progressPercent !== 100) return;

    if (!jobSimulationData?.progress?.conversationId) {
      setJobsimulationTriggerMessage({
        message: `I'm here for the Job Simulation`,
        isTriggered: true,
      });
    }

    const timeoutId = setTimeout(() => {
      setIsChatReady(true);
    }, 200);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [
    jobSimulationData?.progress?.conversationId,
    jobSimulationData?.jobSimulationId,
    progressPercent,
  ]);

  // useEffect(() => {
  //   if (!jobSimulationData?.jobSimulationId || progressPercent !== 100) return;

  //   const timeoutId = setTimeout(() => {
  //     submitMessage({
  //       text: `I'm here for the Job Simulation`,
  //       isTriggered: true,
  //       jobSimulationId: jobSimulationData!.jobSimulationId,
  //     });
  //   }, 1000);

  //   return () => {
  //     if (timeoutId) {
  //       clearTimeout(timeoutId);
  //     }
  //   };
  // }, [jobSimulationData?.jobSimulationId, progressPercent]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between text-sm text-text-secondary">
        <span>Getting job ready...</span>
        <span>{Math.round(progressPercent)}%</span>
      </div>
      <Progress value={progressPercent} className="h-2" />
    </div>
  );
}
