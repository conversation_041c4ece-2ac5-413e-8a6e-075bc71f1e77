import { Button } from "~/components/ui";
import AdOverview from "./AdOverview";
import AdsList from "./AdList";
import AdDetail from "./AdDetail";
import AdAnalytics from "./AdAnalyst";
import PostComposer from "./components/PostComposer";

import { useState } from "react";

export const FacebookAds = () => {
	const [activeTab, setActiveTab] = useState<"overview" | "list">("overview");
	const [currentView, setCurrentView] = useState<"detail" | "analytics" | "composer" | null>(null);
	return (
		<div className="bg-gray-50 h-full">
			{currentView !== "composer" && (
				<div className="px-4 pt-4 flex items-center justify-between">
					<div>
						<p className="text-xl font-medium">Ads</p>
						<p className="text-sm font-normal">Simple ad setup and management for boosted content or individual ads.</p>
					</div>
					<Button className="rounded bg-blue-500 hover:bg-blue-600" onClick={() => setCurrentView("composer")}>Create ad</Button>
				</div>
			)}
			<div className="flex overflow-auto">
				{!currentView && (
					<div className="w-[234px] h-full">
						<div className="px-4 py-6 space-y-2">
							<Button
								className={`w-full bg-transparent justify-start ${activeTab === "overview" ? "bg-black text-white" : "text-black hover:bg-gray-100"}`}
								onClick={() => setActiveTab("overview")}
							>
								📊 Ads summary
							</Button>
							<Button
								className={`w-full bg-transparent justify-start ${activeTab === "list" ? "bg-black text-white" : "text-black hover:bg-gray-100"}`}
								onClick={() => setActiveTab("list")}
							>
								📋 All ads
							</Button>
						</div>
					</div>
				)}
				<div className="p-6 space-y-5 flex-1">
					{currentView === "detail" && <AdDetail onBackToAdList={() => {
						setCurrentView(null);
						setActiveTab("list");
					}} />}
					{currentView === "analytics" && <AdAnalytics onGoBackAdList={() => {
						setCurrentView(null);
						setActiveTab("list");
					}} />}
					{currentView === "composer" && <PostComposer onBackAdList={() => {
						setCurrentView(null);
						setActiveTab("list");
					}} />}
					{!currentView && activeTab === "overview" && <AdOverview />}
					{!currentView && activeTab === "list" && (
						<AdsList
							onGoToDraft={() => setCurrentView("detail")}
							onGoToPostDetail={() => setCurrentView("analytics")}
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default FacebookAds;
