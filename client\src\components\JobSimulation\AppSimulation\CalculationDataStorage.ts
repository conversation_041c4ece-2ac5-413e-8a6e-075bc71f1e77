import { facebookAdCalculationConfig, fbAdScreens } from '~/utils/appSimulationConfig/fbadConfig';
import { xAdCalculationConfig, xAdScreens } from '~/utils/appSimulationConfig/xadConfig';

export const getAppData = (appSimulationId: string) => {
  if (appSimulationId === 'facebook-ad') {
    return {
      appSimulationScreens: fbAdScreens,
      appSimulationConfig: facebookAdCalculationConfig,
    };
  } else if (appSimulationId === 'x-ad') {
    return {
      appSimulationScreens: xAdScreens,
      appSimulationConfig: xAdCalculationConfig,
    };
  }

  return null;
};
