const JobSimulationInteractionService = require('~/server/services/JobSimulation/JobSimulationInteractionService');

const JobSimulationInteractionController = {

  async getUserInteraction(req, res) {
    try {
      console.log("req.query ::: ", req.query)
      const data = await JobSimulationInteractionService.getUserInteraction({
        jobSimulationId: req.query.jobSimulationId,
        jobSimulationEmail: req.query.jobSimulationEmail,
        type: req.query.type,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get user interaction' });
    }
  },

  async deleteUserInteraction(req, res) {
    try {
      const data = await JobSimulationInteractionService.deleteUserInteraction({
        jobSimulationId: req.body.jobSimulationId,
        jobSimulationEmail: req.body.jobSimulationEmail,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to delete user interaction' });
    }
  },

  async saveUserInteraction(req, res) {
    try {
      const data = await JobSimulationInteractionService.saveUserInteraction({
        jobSimulationId: req.body.jobSimulationId,
        jobSimulationEmail: req.body.jobSimulationEmail,
        interaction: req.body.interaction,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to process user interaction' });
    }
  }
};

module.exports = JobSimulationInteractionController;
