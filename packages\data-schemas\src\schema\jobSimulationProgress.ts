import { Document, Schema } from 'mongoose';

export interface IJobSimulationProgress extends Document {
  jobSimulationId: string;
  email: string;
  sessionId: string;
  userId: string;
  intakeId?: string;
  emails: {
    id: string;
    read: boolean;
    time: number;
  }[];
  enabledApps?: string[];
  status: string;
  conversationId?: string;
}

const jobSimulationProgressSchema: Schema<IJobSimulationProgress> = new Schema({
  jobSimulationId: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  userId: {
    type: String,
    required: true,
  },
  intakeId: {
    type: String,
    required: false,
  },
  status: {
    type: String,
    required: true,
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
  },
  emails: {
    type: [
      {
        id: {
          type: String,
          required: true,
        },
        read: {
          type: Boolean,
          required: true,
        },
        time: {
          type: Number,
          required: true,
        },
      },
    ],
    default: undefined,
  },
  enabledApps: {
    type: [String],
    required: false,
    default: ['mail', 'news', 'facebook-ad', 'fb-ad'],
  },
  conversationId: {
    type: String,
    required: false,
  },
});

export default jobSimulationProgressSchema;
